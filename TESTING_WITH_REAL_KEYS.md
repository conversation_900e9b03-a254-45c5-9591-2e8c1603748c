# Testing with Real API Keys - Phase 1 & 2 Validation

## 🔐 Security First
- ✅ Keep real API keys in local `.env` only
- ❌ Never commit real API keys to git repository
- ✅ Use environment variables in production

## 🧪 Testing Checklist with Real Keys

### **1. Database Setup & Migration Testing**
```bash
# Run database migrations
npm run migrate

# Check migration status
npm run migrate:status

# Verify tables were created properly
```

### **2. OpenAI Integration Testing**
```bash
# Test OpenAI service with real API key
npx tsx scripts/final-verification.ts

# Test AI response generation
npm run test test/integration/basic-functionality.test.ts
```

### **3. SMS/Twilio Integration Testing**
- Test STOP/unsubscribe webhook handling
- Verify opt-out status is properly stored
- Test confirmation message sending

### **4. End-to-End Feature Testing**
```bash
# Run all functional tests
npm run test:integration

# Test escalation triggers
# Test inventory import functionality
# Test AI response with real OpenAI
```

### **5. Production Environment Testing**
- Test error handling with real external services
- Verify fallback responses work correctly
- Test database connection pooling
- Validate all environment variables

## 📋 Testing Scenarios

### **Scenario 1: AI Response with Real OpenAI**
1. Set real OPENAI_API_KEY in `.env`
2. Test conversation flow
3. Verify escalation triggers work
4. Check error handling if API is down

### **Scenario 2: SMS Opt-out Flow**
1. Set real Twilio credentials
2. Send test SMS with STOP keyword
3. Verify database records opt-out status
4. Test confirmation message delivery

### **Scenario 3: Database Operations**
1. Set real DATABASE_URL
2. Run all migrations
3. Test inventory import with real data
4. Verify customer opt-out persistence

## ⚠️ Security Reminders

1. **Never commit real API keys**
2. **Use placeholder keys in documentation**
3. **Rotate keys if accidentally exposed**
4. **Use environment-specific configurations**

## 🚀 Ready for Production

Once testing passes with real keys:
- ✅ Code is production-ready
- ✅ Error handling is robust
- ✅ Features work end-to-end
- ✅ Database integration functional