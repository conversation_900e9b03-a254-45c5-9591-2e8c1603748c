/**
 * Simple Platform Test
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('==================================');
console.log('🧪 PLATFORM TEST REPORT');
console.log('==================================');

// Test 1: Check project structure
function checkProjectStructure() {
  console.log('\n🔍 Checking project structure...');
  
  const requiredDirs = ['server', 'client', 'shared'];
  const missingDirs = [];
  
  for (const dir of requiredDirs) {
    if (!fs.existsSync(path.join(__dirname, dir))) {
      missingDirs.push(dir);
    }
  }
  
  if (missingDirs.length === 0) {
    console.log('✅ All required directories present');
    return true;
  } else {
    console.log(`❌ Missing directories: ${missingDirs.join(', ')}`);
    return false;
  }
}

// Test 2: Check configuration files
function checkConfigFiles() {
  console.log('\n🔍 Checking configuration files...');
  
  const requiredFiles = ['.env', 'package.json', 'tsconfig.json'];
  const missingFiles = [];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(__dirname, file))) {
      missingFiles.push(file);
    }
  }
  
  if (missingFiles.length === 0) {
    console.log('✅ All required configuration files present');
    return true;
  } else {
    console.log(`❌ Missing files: ${missingFiles.join(', ')}`);
    return false;
  }
}

// Test 3: Check environment variables
function checkEnvVars() {
  console.log('\n🔍 Checking environment variables...');
  
  try {
    const envContent = fs.readFileSync(path.join(__dirname, '.env'), 'utf8');
    const requiredVars = ['DATABASE_URL', 'SESSION_SECRET', 'NODE_ENV'];
    const missingVars = [];
    
    for (const varName of requiredVars) {
      if (!envContent.includes(varName + '=')) {
        missingVars.push(varName);
      }
    }
    
    if (missingVars.length === 0) {
      console.log('✅ All required environment variables present');
      return true;
    } else {
      console.log(`❌ Missing environment variables: ${missingVars.join(', ')}`);
      return false;
    }
  } catch (error) {
    console.log('❌ Could not read .env file');
    return false;
  }
}

// Test 4: Check server code
function checkServerCode() {
  console.log('\n🔍 Checking server code...');
  
  const serverIndexPath = path.join(__dirname, 'server', 'index.ts');
  
  if (fs.existsSync(serverIndexPath)) {
    console.log('✅ Server entry point exists');
    return true;
  } else {
    console.log('❌ Server entry point missing');
    return false;
  }
}

// Test 5: Check client code
function checkClientCode() {
  console.log('\n🔍 Checking client code...');
  
  const clientIndexPath = path.join(__dirname, 'client', 'src', 'main.tsx');
  
  if (fs.existsSync(clientIndexPath)) {
    console.log('✅ Client entry point exists');
    return true;
  } else {
    console.log('❌ Client entry point missing');
    return false;
  }
}

// Run all tests
async function runTests() {
  const structureResult = checkProjectStructure();
  const configResult = checkConfigFiles();
  const envResult = checkEnvVars();
  const serverResult = checkServerCode();
  const clientResult = checkClientCode();
  
  console.log('\n==================================');
  console.log('📊 TEST RESULTS SUMMARY');
  console.log('==================================');
  console.log(`Project Structure: ${structureResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Configuration Files: ${configResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Environment Variables: ${envResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Server Code: ${serverResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Client Code: ${clientResult ? '✅ PASS' : '❌ FAIL'}`);
  
  const overallResult = structureResult && configResult && envResult && serverResult && clientResult;
  console.log('\n==================================');
  console.log(`Overall Platform Status: ${overallResult ? '✅ READY TO RUN' : '❌ ISSUES DETECTED'}`);
  console.log('==================================');
  
  if (!overallResult) {
    console.log('\nRecommendations:');
    if (!structureResult) console.log('- Check project structure and ensure all directories exist');
    if (!configResult) console.log('- Verify all configuration files are present');
    if (!envResult) console.log('- Complete your .env file with all required variables');
    if (!serverResult) console.log('- Create or fix server entry point');
    if (!clientResult) console.log('- Create or fix client entry point');
  }
  
  return overallResult;
}

runTests();