# Production Readiness Roadmap

## Phase 1: Stabilization & Compliance (Sprint 1)
**Goal**: Close critical gaps that impact reliability and legality.

### a. Essential Feature Gaps (Backend)

#### 1. STOP/Unsubscribe Handling ⚠️ HIGH PRIORITY
- **SMS**: Implement logic in Twilio webhook to detect opt-out keywords:
  - "STOP", "STOPALL", "UNSUBSCRIBE", "QUIT", "END"
  - Mark customer as opted-out in database (add flag to customer/lead table)
  - Automatically respond with confirmation message
- **Email**: Ensure marketing emails include unsubscribe links
  - Link can inform dealership of opt-out request
  - Maintain compliance with CAN-SPAM Act
- **Location**: `server/routes/twilio-webhooks.ts`
- **Database**: Add `opted_out` boolean field to customer/lead tables

#### 2. Integrate Escalation Triggers
- **Automated Escalation**: After each AI response, run through active triggers
  - Use existing `EscalationTriggers` from `server/services/escalation-triggers.ts`
  - Check for keyword matches (legal queries, explicit human requests)
  - Monitor sentiment using `analyzeSentiment()` function
  - If triggered, automatically call `handoverService.createHandover()`
- **Customer Communication**: Update AI response to include escalation message
- **Priority Triggers**:
  - Explicit customer request for human ("speak to a person", "human agent")
  - Legal/compliance queries
  - Negative sentiment below threshold
  - Complex technical questions beyond AI scope

#### 3. Inventory "Not Present" Handling
- **Solution Options**:
  - **Option A**: On each import, collect all processed VINs and mark others as inactive
  - **Option B**: Add `lastSeen` timestamp column to vehicles table
    - Update timestamp on each import
    - Scheduled job to mark inactive vehicles not seen in >30 days
- **Data Integrity**: Prevents stale/noisy inventory data over time
- **Location**: `server/services/inventory-import.ts`

#### 4. OpenAI Error Resilience
- **Retry Mechanism**: Wrap OpenAI calls with retry logic
  - 1 retry on failure after short delay
  - Exponential backoff for multiple failures
- **Fallback Response**: Predefined apology message when OpenAI is down
  - "I'm having trouble accessing information right now, I'll get back to you shortly."
  - Flag conversation for human review
- **Location**: `server/services/openai.ts`
- **Monitoring**: Log OpenAI failures for tracking reliability

### b. Testing and CI/CD Foundation

#### 1. Write Critical Path Tests
- **Integration Tests**: Lead ingestion → AI response flow
  - Mock OpenAI responses to avoid external API dependency
  - Test `/api/inbound` call → AI reply generation in DB
- **Escalation Tests**: Simulate handover scenarios
  - Assert conversation status changes to "escalated"
  - Verify no AI reply sent after escalation
- **Test Locations**:
  - `test/integration/` for end-to-end flows
  - `test/unit/` for individual service tests
- **Frameworks**: Existing Vitest setup with coverage enforcement

#### 2. Critical Test Scenarios
- **Lead Processing**: ADF email → conversation creation → AI response
- **Escalation Flow**: Trigger detection → handover creation → status update
- **Inventory Sync**: Import processing → vehicle updates → availability checks
- **Error Handling**: OpenAI failures → fallback responses → error logging

## Implementation Priority
1. **STOP/Unsubscribe Handling** (Legal compliance - IMMEDIATE)
2. **OpenAI Error Resilience** (System reliability)
3. **Critical Path Tests** (Guard against regressions)
4. **Escalation Triggers** (Business process automation)
5. **Inventory Handling** (Data integrity)

## Success Metrics
- Zero unhandled opt-out requests
- <5% OpenAI failure rate with graceful degradation
- 100% test coverage for critical paths
- Automated escalation for 80%+ of trigger scenarios
- Clean inventory data with <2% stale records

## Next Phase Preview
Phase 2 will focus on performance optimization, enhanced monitoring, and advanced features to maximize commercial potential.