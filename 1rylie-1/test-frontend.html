<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rylie AI Platform Frontend Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input, textarea {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            box-sizing: border-box;
        }
        .conversation {
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
            background: #f9f9f9;
        }
        .message {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .customer { background: #e3f2fd; text-align: right; }
        .ai { background: #f3e5f5; text-align: left; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚗 Rylie AI Platform Frontend Test</h1>
            <p>Testing the Rylie AI automotive dealership platform</p>
        </div>

        <div class="test-section">
            <h3>🔍 API Connection Test</h3>
            <button onclick="testConnection()">Test API Connection</button>
            <div id="connectionResult"></div>
        </div>

        <div class="test-section">
            <h3>💬 Conversation Test</h3>
            <input type="text" id="customerName" placeholder="Customer Name" value="Test Customer">
            <textarea id="messageInput" placeholder="Enter your message..." rows="3">I'm looking for a reliable family car under $30,000. What do you recommend?</textarea>
            <button onclick="testConversation()">Send Message</button>
            <div id="conversationResult"></div>
        </div>

        <div class="test-section">
            <h3>📊 Platform Status</h3>
            <button onclick="checkStatus()">Check Platform Status</button>
            <div id="statusResult"></div>
        </div>

        <div class="test-section">
            <h3>🧪 Automated Tests</h3>
            <button onclick="runAllTests()">Run All Tests</button>
            <div id="testResults"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:5001';

        function showResult(elementId, content, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="test-result ${type}">${content}</div>`;
        }

        async function testConnection() {
            try {
                showResult('connectionResult', '🔄 Testing connection...', 'info');
                
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok) {
                    showResult('connectionResult', `
                        ✅ Connection successful!<br>
                        Platform: ${data.platform}<br>
                        Version: ${data.version}<br>
                        Status: ${data.status}<br>
                        Timestamp: ${data.timestamp}
                    `, 'success');
                } else {
                    showResult('connectionResult', `❌ Connection failed: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('connectionResult', `❌ Connection error: ${error.message}`, 'error');
            }
        }

        async function testConversation() {
            try {
                const customerName = document.getElementById('customerName').value;
                const message = document.getElementById('messageInput').value;
                
                if (!message.trim()) {
                    showResult('conversationResult', '❌ Please enter a message', 'error');
                    return;
                }

                showResult('conversationResult', '🔄 Sending message...', 'info');
                
                const response = await fetch(`${API_BASE}/api/test-conversation`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        message: message,
                        customerName: customerName
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.success) {
                    const conv = data.conversation;
                    let conversationHtml = '<div class="conversation">';
                    
                    conv.messages.forEach(msg => {
                        const messageClass = msg.sender === 'customer' ? 'customer' : 'ai';
                        conversationHtml += `
                            <div class="message ${messageClass}">
                                <strong>${msg.sender === 'customer' ? customerName : 'Rylie AI'}:</strong><br>
                                ${msg.content}
                                <br><small>${new Date(msg.timestamp).toLocaleTimeString()}</small>
                            </div>
                        `;
                    });
                    
                    conversationHtml += '</div>';
                    
                    showResult('conversationResult', `
                        ✅ Conversation successful!<br>
                        Conversation ID: ${conv.id}<br>
                        ${conversationHtml}
                    `, 'success');
                } else {
                    showResult('conversationResult', `❌ Conversation failed: ${data.message || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult('conversationResult', `❌ Conversation error: ${error.message}`, 'error');
            }
        }

        async function checkStatus() {
            try {
                showResult('statusResult', '🔄 Checking platform status...', 'info');
                
                const healthResponse = await fetch(`${API_BASE}/health`);
                const healthData = await healthResponse.json();
                
                let statusHtml = `
                    <strong>Platform Health:</strong> ${healthData.status}<br>
                    <strong>Response Time:</strong> Fast<br>
                    <strong>API Endpoints:</strong> Operational<br>
                    <strong>Last Check:</strong> ${new Date().toLocaleString()}
                `;
                
                showResult('statusResult', statusHtml, 'success');
            } catch (error) {
                showResult('statusResult', `❌ Status check failed: ${error.message}`, 'error');
            }
        }

        async function runAllTests() {
            showResult('testResults', '🔄 Running automated tests...', 'info');
            
            const tests = [
                { name: 'Health Check', test: () => fetch(`${API_BASE}/health`) },
                { name: 'Conversation API', test: () => fetch(`${API_BASE}/api/test-conversation`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ message: 'Test', customerName: 'Test' })
                })},
                { name: '404 Handling', test: () => fetch(`${API_BASE}/nonexistent`) }
            ];
            
            let results = '<strong>Test Results:</strong><br>';
            let passed = 0;
            
            for (const test of tests) {
                try {
                    const response = await test.test();
                    if (test.name === '404 Handling') {
                        if (response.status === 404) {
                            results += `✅ ${test.name}: PASS<br>`;
                            passed++;
                        } else {
                            results += `❌ ${test.name}: FAIL (expected 404, got ${response.status})<br>`;
                        }
                    } else {
                        if (response.ok) {
                            results += `✅ ${test.name}: PASS<br>`;
                            passed++;
                        } else {
                            results += `❌ ${test.name}: FAIL (${response.status})<br>`;
                        }
                    }
                } catch (error) {
                    results += `❌ ${test.name}: ERROR (${error.message})<br>`;
                }
            }
            
            results += `<br><strong>Summary:</strong> ${passed}/${tests.length} tests passed`;
            
            showResult('testResults', results, passed === tests.length ? 'success' : 'error');
        }

        // Auto-test connection on page load
        window.onload = () => {
            testConnection();
        };
    </script>
</body>
</html>
