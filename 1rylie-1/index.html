<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>RylieAI Platform</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f5f7fa;
      margin: 0;
      padding: 0;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    .auth-container {
      display: flex;
      min-height: 100vh;
    }
    
    .auth-form {
      width: 400px;
      margin: auto;
      padding: 30px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    }
    
    .auth-logo {
      text-align: center;
      margin-bottom: 20px;
    }
    
    .auth-logo h1 {
      font-size: 28px;
      color: #4f46e5;
      margin: 0;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: #4b5563;
    }
    
    input {
      width: 100%;
      padding: 12px;
      border: 1px solid #d1d5db;
      border-radius: 4px;
      box-sizing: border-box;
      font-size: 16px;
    }
    
    button {
      background-color: #4f46e5;
      color: white;
      border: none;
      padding: 12px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      font-weight: 500;
      width: 100%;
      transition: background-color 0.2s;
    }
    
    button:hover {
      background-color: #4338ca;
    }
    
    .quick-login {
      margin: 20px 0;
    }
    
    .quick-login-title {
      text-align: center;
      margin-bottom: 10px;
      font-weight: 500;
      color: #6b7280;
    }
    
    .quick-login-buttons {
      display: grid;
      grid-template-columns: 1fr;
      gap: 8px;
    }
    
    .quick-login-btn {
      background-color: #f3f4f6;
      color: #4b5563;
      border: 1px solid #e5e7eb;
      padding: 10px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      text-align: left;
      transition: background-color 0.2s;
    }
    
    .quick-login-btn:hover {
      background-color: #e5e7eb;
    }
    
    .error-message {
      color: #ef4444;
      margin-top: 10px;
      font-size: 14px;
    }
    
    .dashboard {
      display: none;
      padding: 20px;
    }
    
    .dashboard-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 30px;
      padding-bottom: 10px;
      border-bottom: 1px solid #e5e7eb;
    }
    
    .user-info {
      display: flex;
      align-items: center;
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background-color: #4f46e5;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: bold;
      margin-right: 10px;
    }
    
    .logout-btn {
      background-color: transparent;
      color: #6b7280;
      border: 1px solid #d1d5db;
      padding: 8px 16px;
      width: auto;
    }
    
    .logout-btn:hover {
      background-color: #f3f4f6;
      color: #4b5563;
    }
    
    .dashboard-content {
      display: grid;
      grid-template-columns: 250px 1fr;
      gap: 20px;
    }
    
    .sidebar {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 15px;
    }
    
    .sidebar-menu {
      list-style-type: none;
      padding: 0;
      margin: 0;
    }
    
    .sidebar-menu li {
      margin-bottom: 5px;
    }
    
    .sidebar-menu a {
      display: block;
      padding: 10px 15px;
      color: #4b5563;
      text-decoration: none;
      border-radius: 4px;
      transition: background-color 0.2s;
    }
    
    .sidebar-menu a:hover, .sidebar-menu a.active {
      background-color: #f3f4f6;
      color: #4f46e5;
    }
    
    .main-content {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 20px;
    }
    
    .card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 20px;
      margin-bottom: 20px;
    }
    
    .card-title {
      font-size: 18px;
      font-weight: 600;
      margin-top: 0;
      margin-bottom: 15px;
      color: #111827;
    }
    
    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      margin-bottom: 30px;
    }
    
    .stat-card {
      background-color: white;
      border-radius: 8px;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      padding: 15px;
    }
    
    .stat-value {
      font-size: 24px;
      font-weight: 700;
      color: #111827;
      margin: 0;
    }
    
    .stat-label {
      font-size: 14px;
      color: #6b7280;
      margin: 5px 0 0 0;
    }
    
    table {
      width: 100%;
      border-collapse: collapse;
    }
    
    th, td {
      padding: 12px 15px;
      text-align: left;
      border-bottom: 1px solid #e5e7eb;
    }
    
    th {
      font-weight: 500;
      color: #6b7280;
      background-color: #f9fafb;
    }
    
    tr:last-child td {
      border-bottom: none;
    }
  </style>
</head>
<body>
  <div class="auth-container" id="login-page">
    <div class="auth-form">
      <div class="auth-logo">
        <h1>RylieAI Platform</h1>
        <p>Login to access your dashboard</p>
      </div>
      
      <div id="login-error" class="error-message"></div>
      
      <form id="login-form">
        <div class="form-group">
          <label for="username">Username</label>
          <input type="text" id="username" name="username" required>
        </div>
        
        <div class="form-group">
          <label for="password">Password</label>
          <input type="password" id="password" name="password" required>
        </div>
        
        <button type="submit" id="login-button">Login</button>
      </form>
      
      <div class="quick-login">
        <div class="quick-login-title">Quick Login Options</div>
        <div class="quick-login-buttons">
          <button class="quick-login-btn" data-username="superadmin" data-password="password123">
            Super Admin (superadmin)
          </button>
          <button class="quick-login-btn" data-username="luxuryadmin" data-password="password123">
            Dealership Admin (luxuryadmin)
          </button>
          <button class="quick-login-btn" data-username="luxurysales" data-password="password123">
            Sales Representative (luxurysales)
          </button>
        </div>
      </div>
    </div>
  </div>
  
  <div class="dashboard" id="dashboard-page">
    <div class="container">
      <div class="dashboard-header">
        <h1>RylieAI Dashboard</h1>
        
        <div class="user-info">
          <div class="user-avatar" id="user-avatar">U</div>
          <div>
            <div id="user-name">User Name</div>
            <div id="user-role" style="font-size: 14px; color: #6b7280;">Role</div>
          </div>
          <button class="logout-btn" id="logout-button" style="margin-left: 15px;">Logout</button>
        </div>
      </div>
      
      <div class="dashboard-content">
        <div class="sidebar">
          <ul class="sidebar-menu">
            <li><a href="#" class="active">Dashboard</a></li>
            <li><a href="#">Dealerships</a></li>
            <li><a href="#">Users</a></li>
            <li><a href="#">Analytics</a></li>
            <li><a href="#">Settings</a></li>
          </ul>
        </div>
        
        <div class="main-content">
          <div class="stats-grid">
            <div class="stat-card">
              <p class="stat-value">3</p>
              <p class="stat-label">Dealerships</p>
            </div>
            <div class="stat-card">
              <p class="stat-value">12</p>
              <p class="stat-label">Active Users</p>
            </div>
            <div class="stat-card">
              <p class="stat-value">128</p>
              <p class="stat-label">Conversations</p>
            </div>
            <div class="stat-card">
              <p class="stat-value">98%</p>
              <p class="stat-label">Satisfaction Rate</p>
            </div>
          </div>
          
          <div class="card">
            <h2 class="card-title">Recent Activity</h2>
            <table>
              <thead>
                <tr>
                  <th>User</th>
                  <th>Action</th>
                  <th>Dealership</th>
                  <th>Date</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>John Smith</td>
                  <td>Created new prompt</td>
                  <td>Luxury Auto</td>
                  <td>Today, 10:45 AM</td>
                </tr>
                <tr>
                  <td>Sarah Johnson</td>
                  <td>Updated settings</td>
                  <td>Premium Motors</td>
                  <td>Today, 9:30 AM</td>
                </tr>
                <tr>
                  <td>Michael Brown</td>
                  <td>Added new user</td>
                  <td>City Dealership</td>
                  <td>Yesterday, 4:15 PM</td>
                </tr>
                <tr>
                  <td>Emma Wilson</td>
                  <td>Modified template</td>
                  <td>Luxury Auto</td>
                  <td>Yesterday, 2:30 PM</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Check if user is already logged in
      checkAuthStatus();
      
      // Setup login form
      document.getElementById('login-form').addEventListener('submit', function(e) {
        e.preventDefault();
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        login(username, password);
      });
      
      // Setup quick login buttons
      document.querySelectorAll('.quick-login-btn').forEach(button => {
        button.addEventListener('click', function() {
          const username = this.getAttribute('data-username');
          const password = this.getAttribute('data-password');
          
          document.getElementById('username').value = username;
          document.getElementById('password').value = password;
          
          login(username, password);
        });
      });
      
      // Setup logout button
      document.getElementById('logout-button').addEventListener('click', logout);
    });
    
    // Authentication functions
    async function checkAuthStatus() {
      try {
        const response = await fetch('/api/user', {
          credentials: 'include'
        });
        
        if (response.ok) {
          const user = await response.json();
          showDashboard(user);
        } else {
          showLoginPage();
        }
      } catch (error) {
        console.error('Error checking auth status:', error);
        showLoginPage();
      }
    }
    
    async function login(username, password) {
      const errorElement = document.getElementById('login-error');
      const loginButton = document.getElementById('login-button');
      
      // Clear previous errors
      errorElement.textContent = '';
      
      // Disable login button and show loading state
      loginButton.disabled = true;
      loginButton.textContent = 'Logging in...';
      
      try {
        const response = await fetch('/api/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          credentials: 'include',
          body: JSON.stringify({ username, password })
        });
        
        if (response.ok) {
          const user = await response.json();
          showDashboard(user);
        } else {
          const error = await response.json();
          errorElement.textContent = error.error || 'Invalid username or password';
        }
      } catch (error) {
        console.error('Login error:', error);
        errorElement.textContent = 'Failed to connect to the server. Please try again.';
      } finally {
        // Re-enable login button
        loginButton.disabled = false;
        loginButton.textContent = 'Login';
      }
    }
    
    async function logout() {
      try {
        await fetch('/api/logout', {
          method: 'POST',
          credentials: 'include'
        });
        
        showLoginPage();
      } catch (error) {
        console.error('Logout error:', error);
      }
    }
    
    // UI functions
    function showLoginPage() {
      document.getElementById('login-page').style.display = 'flex';
      document.getElementById('dashboard-page').style.display = 'none';
    }
    
    function showDashboard(user) {
      // Update user info
      document.getElementById('user-name').textContent = user.name || user.username;
      document.getElementById('user-role').textContent = user.role || 'User';
      document.getElementById('user-avatar').textContent = (user.name || user.username).charAt(0).toUpperCase();
      
      // Show dashboard page
      document.getElementById('login-page').style.display = 'none';
      document.getElementById('dashboard-page').style.display = 'block';
    }
  </script>
</body>
</html>