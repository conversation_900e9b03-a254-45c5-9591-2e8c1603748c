/**
 * Schema resolver to prevent duplicate schema definitions
 *
 * This file serves as a central point for resolving schema imports
 * to prevent TypeScript compilation errors from duplicate definitions.
 */

// Re-export from schema.ts (base schema)
export * from './schema';

// Re-export from enhanced-schema.ts (enhanced schema that extends base schema)
export {
  customers,
  messages,
  conversations,
  customersRelations,
  conversationsRelations,
  messagesRelations,
  insertCustomerSchema,
  insertMessageSchema,
  insertConversationSchema,
  Customer,
  InsertCustomer,
  Message,
  InsertMessage,
  Conversation,
  InsertConversation
} from './enhanced-schema';

// Re-export from lead-management-schema.ts
export {
  leadSources,
  leadStatuses,
  leadPriorities,
  conversationStatuses,
  messageTypes,
  messageSenders,
  handoverReasons,
  handoverStatuses,
  leadSourcesTable,
  vehicleInterests,
  leads,
  handovers,
  leadActivities,
  leadSourcesRelations,
  vehicleInterestsRelations,
  leadsRelations,
  handoversRelations,
  leadActivitiesRelations,
  insertLeadSourceSchema,
  insertVehicleInterestSchema,
  insertLeadSchema,
  insertHandoverSchema,
  insertLeadActivitySchema,
  LeadSource,
  LeadStatus,
  LeadPriority,
  ConversationStatus,
  MessageType,
  MessageSender,
  HandoverReason,
  HandoverStatus,
  LeadSourceTable,
  InsertLeadSource,
  VehicleInterest,
  InsertVehicleInterest,
  Lead,
  InsertLead,
  Handover,
  InsertHandover,
  LeadActivity,
  InsertLeadActivity,
  inboundLeadSchema,
  replyMessageSchema,
  handoverRequestSchema
} from './lead-management-schema';

// Re-export from adf-schema.ts
export {
  adfLeadStatuses,
  adfRequestTypes,
  adfProcessingStatuses,
  adfLeads,
  adfProcessingLogs,
  adfEmailQueue,
  adfLeadsRelations,
  adfProcessingLogsRelations,
  adfEmailQueueRelations,
  insertAdfLeadSchema,
  insertAdfProcessingLogSchema,
  insertAdfEmailQueueSchema,
  AdfLeadStatus,
  AdfRequestType,
  AdfProcessingStatus,
  AdfLead,
  InsertAdfLead,
  AdfProcessingLog,
  InsertAdfProcessingLog,
  AdfEmailQueue,
  InsertAdfEmailQueue,
  AdfXmlStructure
} from './adf-schema';

// Re-export from schema-extensions.ts
export {
  escalationTriggers,
  leadScores,
  followUps,
  userInvitations,
  auditLogs,
  customerProfiles,
  customerInteractions,
  insertEscalationTriggerSchema,
  insertLeadScoreSchema,
  insertFollowUpSchema,
  insertUserInvitationSchema,
  insertAuditLogSchema,
  insertCustomerProfileSchema,
  insertCustomerInteractionSchema,
  EscalationTrigger,
  InsertEscalationTrigger,
  LeadScore,
  InsertLeadScore,
  FollowUp,
  InsertFollowUp,
  UserInvitation,
  InsertUserInvitation,
  AuditLog,
  InsertAuditLog,
  CustomerProfile,
  InsertCustomerProfile,
  CustomerInteraction,
  InsertCustomerInteraction
} from './schema-extensions';

// Re-export from api-schemas.ts
export {
  paginationSchema,
  apiResponseSchema,
  errorResponseSchema,
  validationErrorSchema,
  validationErrorResponseSchema,
  customerSchema,
  customerInputSchema,
  vehicleInterestSchema,
  vehicleInterestInputSchema,
  leadSchema,
  inboundLeadRequestSchema,
  leadCreationResponseSchema,
  replyMessageRequestSchema,
  messageResponseSchema,
  handoverRequestSchema as apiHandoverRequestSchema,
  handoverResponseSchema,
  handoverUpdateRequestSchema,
  conversationSchema,
  messageSchema,
  leadsQuerySchema,
  conversationsQuerySchema,
  leadListResponseSchema,
  leadDetailResponseSchema,
  conversationListResponseSchema,
  conversationDetailResponseSchema,
  twilioWebhookSchema,
  InboundLeadRequest,
  LeadCreationResponse,
  ReplyMessageRequest,
  MessageResponse,
  HandoverRequest,
  HandoverResponse,
  HandoverUpdateRequest,
  LeadsQuery,
  ConversationsQuery,
  TwilioWebhook,
  ApiResponse,
  ErrorResponse,
  ValidationErrorResponse
} from './api-schemas';