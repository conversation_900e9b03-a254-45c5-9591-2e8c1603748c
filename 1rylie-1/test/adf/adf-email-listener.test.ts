import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { EventEmitter } from 'events';
import { AdfEmailListener } from '../../server/services/adf-email-listener';
import { ParsedMail } from 'mailparser';

// Mock IMAP
jest.mock('imap', () => ({
  __esModule: true,
  default: jest.fn().mockImplementation(() => ({
    connect: jest.fn(),
    openBox: jest.fn(),
    search: jest.fn(),
    fetch: jest.fn(),
    move: jest.fn(),
    end: jest.fn(),
    on: jest.fn(),
    once: jest.fn(),
  }))
}));

// Mock mailparser
jest.mock('mailparser', () => ({
  simpleParser: jest.fn()
}));

const mockImapConfig = {
  user: '<EMAIL>',
  password: 'password',
  host: 'imap.example.com',
  port: 993,
  tls: true,
  tlsOptions: { rejectUnauthorized: false }
};

describe('AdfEmailListener', () => {
  let emailListener: AdfEmailListener;
  let mockImap: any;

  beforeEach(() => {
    emailListener = new AdfEmailListener(mockImapConfig);
    mockImap = emailListener['imapConnection'];
  });

  afterEach(() => {
    jest.clearAllMocks();
    emailListener.removeAllListeners();
  });

  describe('Email Processing', () => {
    it('should extract ADF content from email body', async () => {
      const mockEmail: ParsedMail = {
        messageId: 'test-123',
        subject: 'New Lead',
        from: { value: [{ address: '<EMAIL>', name: 'Lead Source' }] },
        date: new Date(),
        text: `
          <?xml version="1.0" encoding="UTF-8"?>
          <adf>
            <prospect>
              <requestdate>2024-01-15T10:30:00</requestdate>
              <customer>
                <contact>
                  <name part="first">John</name>
                  <name part="last">Doe</name>
                  <email><EMAIL></email>
                  <phone>************</phone>
                </contact>
              </customer>
              <vehicle>
                <year>2024</year>
                <make>Toyota</make>
                <model>Camry</model>
              </vehicle>
              <vendor>
                <vendorname>Test Dealership</vendorname>
                <contact>
                  <email><EMAIL></email>
                </contact>
              </vendor>
            </prospect>
          </adf>
        `,
        html: false,
        attachments: []
      };

      const result = await emailListener['extractAdfContent'](mockEmail);
      
      expect(result).toContain('<adf>');
      expect(result).toContain('<prospect>');
      expect(result).toContain('John');
      expect(result).toContain('Doe');
    });

    it('should extract ADF content from XML attachment', async () => {
      const adfXmlContent = `<?xml version="1.0" encoding="UTF-8"?>
        <adf>
          <prospect>
            <requestdate>2024-01-15T10:30:00</requestdate>
            <customer>
              <contact>
                <name part="first">Jane</name>
                <name part="last">Smith</name>
                <email><EMAIL></email>
              </contact>
            </customer>
            <vehicle>
              <year>2023</year>
              <make>Honda</make>
              <model>Civic</model>
            </vehicle>
            <vendor>
              <vendorname>Honda Dealership</vendorname>
            </vendor>
          </prospect>
        </adf>`;

      const mockEmail: ParsedMail = {
        messageId: 'test-456',
        subject: 'Lead Attachment',
        from: { value: [{ address: '<EMAIL>', name: 'Honda Leads' }] },
        date: new Date(),
        text: 'Please find attached lead',
        html: false,
        attachments: [{
          filename: 'lead.xml',
          contentType: 'application/xml',
          content: Buffer.from(adfXmlContent),
          size: adfXmlContent.length
        }]
      };

      const result = await emailListener['extractAdfContent'](mockEmail);
      
      expect(result).toContain('<adf>');
      expect(result).toContain('Jane');
      expect(result).toContain('Smith');
      expect(result).toContain('Honda');
    });

    it('should handle emails without ADF content', async () => {
      const mockEmail: ParsedMail = {
        messageId: 'test-789',
        subject: 'Regular Email',
        from: { value: [{ address: '<EMAIL>', name: 'Someone' }] },
        date: new Date(),
        text: 'This is just a regular email without any ADF content.',
        html: false,
        attachments: []
      };

      const result = await emailListener['extractAdfContent'](mockEmail);
      
      expect(result).toBeNull();
    });
  });

  describe('Configuration Validation', () => {
    it('should validate IMAP configuration on creation', () => {
      expect(() => {
        new AdfEmailListener({
          user: '',
          password: 'password',
          host: 'imap.example.com',
          port: 993,
          tls: true
        });
      }).toThrow('IMAP user is required');

      expect(() => {
        new AdfEmailListener({
          user: '<EMAIL>',
          password: '',
          host: 'imap.example.com',
          port: 993,
          tls: true
        });
      }).toThrow('IMAP password is required');

      expect(() => {
        new AdfEmailListener({
          user: '<EMAIL>',
          password: 'password',
          host: '',
          port: 993,
          tls: true
        });
      }).toThrow('IMAP host is required');
    });
  });
});