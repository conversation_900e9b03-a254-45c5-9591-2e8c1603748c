/**
 * Sample ADF XML data for testing based on ADF 1.0 specification
 * Includes valid, invalid, and edge case scenarios
 */

// Valid ADF XML - Complete lead with all fields
export const VALID_COMPLETE_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <vehicle>
      <year>2024</year>
      <make>Honda</make>
      <model>Accord</model>
      <trim>Sport</trim>
      <vin>1HGCV1F30PA123456</vin>
      <stock>H24001</stock>
      <condition>New</condition>
      <price>32500.00</price>
      <mileage>12</mileage>
    </vehicle>
    <customer>
      <contact>
        <name>
          <part type="first">John</part>
          <part type="last">Smith</part>
        </name>
        <email><EMAIL></email>
        <phone type="voice" time="day">******-123-4567</phone>
        <address>
          <street>123 Main Street</street>
          <apartment>Apt 4B</apartment>
          <city>Springfield</city>
          <regioncode>IL</regioncode>
          <postalcode>62701</postalcode>
          <country>US</country>
        </address>
      </contact>
    </customer>
    <vendor>
      <vendorname>Springfield Honda</vendorname>
      <contact>
        <email><EMAIL></email>
        <phone>******-987-6543</phone>
        <address>
          <street>456 Auto Plaza Drive</street>
          <city>Springfield</city>
          <regioncode>IL</regioncode>
          <postalcode>62702</postalcode>
        </address>
      </contact>
    </vendor>
    <provider>
      <name>AutoTrader.com</name>
      <email><EMAIL></email>
      <phone>******-AUTOTRADER</phone>
      <service>AutoTrader</service>
    </provider>
    <comments>Looking for a reliable sedan with good fuel economy. Interested in financing options.</comments>
    <timeframe>Within 2 weeks</timeframe>
    <trade>
      <vehicle>
        <year>2018</year>
        <make>Toyota</make>
        <model>Camry</model>
        <trim>LE</trim>
        <vin>4T1BF1FK5JU123456</vin>
        <mileage>45000</mileage>
        <condition>Good</condition>
        <value>18500.00</value>
      </vehicle>
    </trade>
  </prospect>
</adf>`;

// Valid ADF XML - Minimal required fields only
export const VALID_MINIMAL_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Jane Doe</part>
        </name>
      </contact>
    </customer>
  </prospect>
</adf>`;

// Valid ADF XML - US date format
export const VALID_US_DATE_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>01/15/2024</requestdate>
    <customer>
      <contact>
        <name>
          <part type="first">Mike</part>
          <part type="last">Johnson</part>
        </name>
        <email><EMAIL></email>
        <phone>************</phone>
      </contact>
    </customer>
    <vendor>
      <vendorname>City Motors</vendorname>
    </vendor>
  </prospect>
</adf>`;

// Valid ADF XML - Simple string name format
export const VALID_SIMPLE_NAME_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15</requestdate>
    <customer>
      <contact>
        <name>Sarah Wilson</name>
        <email><EMAIL></email>
        <phone type="cellphone">************</phone>
      </contact>
    </customer>
    <vehicle>
      <year>2023</year>
      <make>Ford</make>
      <model>F-150</model>
    </vehicle>
  </prospect>
</adf>`;

// Invalid ADF XML - Missing root element
export const INVALID_NO_ROOT_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<prospect>
  <requestdate>2024-01-15T10:30:00Z</requestdate>
  <customer>
    <contact>
      <name>
        <part type="full">Invalid Lead</part>
      </name>
    </contact>
  </customer>
</prospect>`;

// Invalid ADF XML - Missing version attribute
export const INVALID_NO_VERSION_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf>
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">No Version Lead</part>
        </name>
      </contact>
    </customer>
  </prospect>
</adf>`;

// Invalid ADF XML - Missing prospect element
export const INVALID_NO_PROSPECT_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <customer>
    <contact>
      <name>
        <part type="full">No Prospect Lead</part>
      </name>
    </contact>
  </customer>
</adf>`;

// Invalid ADF XML - Missing required fields
export const INVALID_MISSING_REQUIRED_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <vehicle>
      <make>Honda</make>
      <model>Civic</model>
    </vehicle>
  </prospect>
</adf>`;

// Invalid ADF XML - Bad email format
export const INVALID_EMAIL_FORMAT_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Bad Email Lead</part>
        </name>
        <email>not-an-email</email>
      </contact>
    </customer>
  </prospect>
</adf>`;

// Invalid ADF XML - Bad date format
export const INVALID_DATE_FORMAT_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>invalid-date-format</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Bad Date Lead</part>
        </name>
      </contact>
    </customer>
  </prospect>
</adf>`;

// Malformed XML - Syntax error
export const MALFORMED_XML = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Malformed XML Lead
        </name>
      </contact>
    </customer>
  </prospect>
</adf>`;

// Edge case - Multiple phone numbers
export const EDGE_CASE_MULTIPLE_PHONES_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Multi Phone Lead</part>
        </name>
        <phone type="voice" time="day">************</phone>
        <phone type="cellphone" time="evening">************</phone>
        <phone type="fax">************</phone>
      </contact>
    </customer>
  </prospect>
</adf>`;

// Edge case - Very long content
export const EDGE_CASE_LONG_CONTENT_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="first">VeryLongFirstNameThatExceedsNormalLimitsAndTestsFieldLengthValidation</part>
          <part type="last">VeryLongLastNameThatAlsoExceedsNormalLimitsForTestingPurposes</part>
        </name>
        <email><EMAIL></email>
      </contact>
    </customer>
    <comments>This is a very long comment that contains a lot of detailed information about what the customer is looking for including specific requirements for the vehicle such as color preferences, feature requirements, budget constraints, timeline expectations, financing needs, trade-in considerations, and many other details that might be included in a real-world lead submission. This text is intentionally long to test how the system handles large amounts of comment data and ensures that there are no issues with field length limitations or processing of extensive customer input.</comments>
  </prospect>
</adf>`;

// Edge case - Special characters and encoding
export const EDGE_CASE_SPECIAL_CHARS_ADF = `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="first">José</part>
          <part type="last">García-Rodriguez</part>
        </name>
        <email>jose.garcia@señor.com</email>
        <phone>******-123-4567 ext. 123</phone>
        <address>
          <street>123 Main St. #4B</street>
          <city>São Paulo</city>
          <regioncode>SP</regioncode>
          <postalcode>01234-567</postalcode>
          <country>BR</country>
        </address>
      </contact>
    </customer>
    <comments>Looking for a car with "premium" features & excellent fuel economy. Budget: $25,000-$30,000. Must have < 50k miles!</comments>
  </prospect>
</adf>`;

// Test data for deduplication scenarios
export const DUPLICATE_LEAD_SET = [
  // Original lead
  `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Duplicate Test Lead</part>
        </name>
        <email><EMAIL></email>
        <phone>************</phone>
      </contact>
    </customer>
    <vendor>
      <vendorname>Test Dealership</vendorname>
    </vendor>
  </prospect>
</adf>`,
  
  // Exact duplicate (should be caught)
  `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Duplicate Test Lead</part>
        </name>
        <email><EMAIL></email>
        <phone>************</phone>
      </contact>
    </customer>
    <vendor>
      <vendorname>Test Dealership</vendorname>
    </vendor>
  </prospect>
</adf>`,
  
  // Similar but different customer (should NOT be caught as duplicate)
  `<?xml version="1.0" encoding="UTF-8"?>
<adf version="1.0">
  <prospect>
    <requestdate>2024-01-15T10:30:00Z</requestdate>
    <customer>
      <contact>
        <name>
          <part type="full">Different Test Lead</part>
        </name>
        <email><EMAIL></email>
        <phone>************</phone>
      </contact>
    </customer>
    <vendor>
      <vendorname>Test Dealership</vendorname>
    </vendor>
  </prospect>
</adf>`
];

// Expected parsed results for testing
export interface ExpectedLeadData {
  customerFullName: string;
  customerEmail?: string;
  customerPhone?: string;
  vehicleMake?: string;
  vehicleModel?: string;
  vehicleYear?: number;
  vendorName?: string;
  providerName?: string;
  comments?: string;
  requestDate: Date;
}

export const EXPECTED_COMPLETE_LEAD: ExpectedLeadData = {
  customerFullName: 'John Smith',
  customerEmail: '<EMAIL>',
  customerPhone: '******-123-4567',
  vehicleMake: 'Honda',
  vehicleModel: 'Accord',
  vehicleYear: 2024,
  vendorName: 'Springfield Honda',
  providerName: 'AutoTrader.com',
  comments: 'Looking for a reliable sedan with good fuel economy. Interested in financing options.',
  requestDate: new Date('2024-01-15T10:30:00Z')
};

export const EXPECTED_MINIMAL_LEAD: ExpectedLeadData = {
  customerFullName: 'Jane Doe',
  requestDate: new Date('2024-01-15T10:30:00Z')
};

export const EXPECTED_SIMPLE_NAME_LEAD: ExpectedLeadData = {
  customerFullName: 'Sarah Wilson',
  customerEmail: '<EMAIL>',
  customerPhone: '************',
  vehicleMake: 'Ford',
  vehicleModel: 'F-150',
  vehicleYear: 2023,
  requestDate: new Date('2024-01-15')
};

// Test scenarios for comprehensive testing
export const TEST_SCENARIOS = [
  {
    name: 'Valid Complete ADF',
    xml: VALID_COMPLETE_ADF,
    expected: EXPECTED_COMPLETE_LEAD,
    shouldSucceed: true
  },
  {
    name: 'Valid Minimal ADF',
    xml: VALID_MINIMAL_ADF,
    expected: EXPECTED_MINIMAL_LEAD,
    shouldSucceed: true
  },
  {
    name: 'Valid US Date Format',
    xml: VALID_US_DATE_ADF,
    expected: {
      customerFullName: 'Mike Johnson',
      customerEmail: '<EMAIL>',
      customerPhone: '************',
      vendorName: 'City Motors',
      requestDate: new Date('2024-01-15')
    },
    shouldSucceed: true
  },
  {
    name: 'Valid Simple Name Format',
    xml: VALID_SIMPLE_NAME_ADF,
    expected: EXPECTED_SIMPLE_NAME_LEAD,
    shouldSucceed: true
  },
  {
    name: 'Invalid - No Root Element',
    xml: INVALID_NO_ROOT_ADF,
    expected: null,
    shouldSucceed: false,
    expectedErrors: ['Missing root <adf> element']
  },
  {
    name: 'Invalid - No Version',
    xml: INVALID_NO_VERSION_ADF,
    expected: null,
    shouldSucceed: false,
    expectedErrors: ['Missing ADF version attribute']
  },
  {
    name: 'Invalid - No Prospect',
    xml: INVALID_NO_PROSPECT_ADF,
    expected: null,
    shouldSucceed: false,
    expectedErrors: ['Missing <prospect> element']
  },
  {
    name: 'Invalid - Missing Required Fields',
    xml: INVALID_MISSING_REQUIRED_ADF,
    expected: null,
    shouldSucceed: false,
    expectedErrors: ['Missing required field: adf.prospect.requestdate', 'Missing required field: adf.prospect.customer.contact.name']
  },
  {
    name: 'Invalid - Bad Email Format',
    xml: INVALID_EMAIL_FORMAT_ADF,
    expected: null,
    shouldSucceed: false,
    expectedErrors: ['Invalid email format: not-an-email']
  },
  {
    name: 'Malformed XML',
    xml: MALFORMED_XML,
    expected: null,
    shouldSucceed: false,
    expectedErrors: ['XML syntax error']
  }
];

export default {
  VALID_COMPLETE_ADF,
  VALID_MINIMAL_ADF,
  VALID_US_DATE_ADF,
  VALID_SIMPLE_NAME_ADF,
  INVALID_NO_ROOT_ADF,
  INVALID_NO_VERSION_ADF,
  INVALID_NO_PROSPECT_ADF,
  INVALID_MISSING_REQUIRED_ADF,
  INVALID_EMAIL_FORMAT_ADF,
  INVALID_DATE_FORMAT_ADF,
  MALFORMED_XML,
  EDGE_CASE_MULTIPLE_PHONES_ADF,
  EDGE_CASE_LONG_CONTENT_ADF,
  EDGE_CASE_SPECIAL_CHARS_ADF,
  DUPLICATE_LEAD_SET,
  EXPECTED_COMPLETE_LEAD,
  EXPECTED_MINIMAL_LEAD,
  EXPECTED_SIMPLE_NAME_LEAD,
  TEST_SCENARIOS
};