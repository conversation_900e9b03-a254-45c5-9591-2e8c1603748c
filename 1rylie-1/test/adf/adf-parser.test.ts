import { describe, test, expect, beforeEach } from '@jest/globals';
import { <PERSON>f<PERSON><PERSON><PERSON>, AdfSchemaValidator } from '../../server/services/adf-parser';
import { TEST_SCENARIOS, DUPLICATE_LEAD_SET, EDGE_CASE_MULTIPLE_PHONES_ADF, EDGE_CASE_LONG_CONTENT_ADF, EDGE_CASE_SPECIAL_CHARS_ADF } from './sample-adf-data';

describe('ADF Parser', () => {
  let parser: AdfParser;

  beforeEach(() => {
    parser = new AdfParser();
  });

  describe('XML Parsing and Validation', () => {
    describe('Valid ADF scenarios', () => {
      TEST_SCENARIOS.filter(scenario => scenario.shouldSucceed).forEach(({ name, xml, expected }) => {
        test(`should successfully parse valid ADF: ${name}`, async () => {
          const result = await parser.parseAdfXml(xml);
          
          expect(result.success).toBe(true);
          expect(result.errors).toHaveLength(0);
          expect(result.parsedData).toBeDefined();
          expect(result.mappedLead).toBeDefined();
          
          if (result.mappedLead && expected) {
            expect(result.mappedLead.customerFullName).toBe(expected.customerFullName);
            if (expected.customerEmail) {
              expect(result.mappedLead.customerEmail).toBe(expected.customerEmail);
            }
            if (expected.customerPhone) {
              expect(result.mappedLead.customerPhone).toBe(expected.customerPhone);
            }
            if (expected.vehicleMake) {
              expect(result.mappedLead.vehicleMake).toBe(expected.vehicleMake);
            }
            if (expected.vehicleModel) {
              expect(result.mappedLead.vehicleModel).toBe(expected.vehicleModel);
            }
            if (expected.vehicleYear) {
              expect(result.mappedLead.vehicleYear).toBe(expected.vehicleYear);
            }
            if (expected.vendorName) {
              expect(result.mappedLead.vendorName).toBe(expected.vendorName);
            }
            if (expected.comments) {
              expect(result.mappedLead.comments).toBe(expected.comments);
            }
          }
        });
      });
    });

    describe('Invalid ADF scenarios', () => {
      TEST_SCENARIOS.filter(scenario => !scenario.shouldSucceed).forEach(({ name, xml, expectedErrors }) => {
        test(`should reject invalid ADF: ${name}`, async () => {
          const result = await parser.parseAdfXml(xml);
          
          expect(result.success).toBe(false);
          expect(result.errors.length).toBeGreaterThan(0);
          
          if (expectedErrors) {
            expectedErrors.forEach(expectedError => {
              expect(result.errors.some(error => error.includes(expectedError))).toBe(true);
            });
          }
        });
      });
    });
  });

  describe('Data Extraction', () => {
    test('should extract complete customer information', async () => {
      const result = await parser.parseAdfXml(TEST_SCENARIOS[0].xml); // Valid complete ADF
      
      expect(result.success).toBe(true);
      expect(result.mappedLead).toBeDefined();
      
      const lead = result.mappedLead!;
      expect(lead.customerFullName).toBe('John Smith');
      expect(lead.customerFirstName).toBe('John');
      expect(lead.customerLastName).toBe('Smith');
      expect(lead.customerEmail).toBe('<EMAIL>');
      expect(lead.customerPhone).toBe('******-123-4567');
      expect(lead.customerAddress).toBe('123 Main Street, Apt 4B');
      expect(lead.customerCity).toBe('Springfield');
      expect(lead.customerState).toBe('IL');
      expect(lead.customerZip).toBe('62701');
      expect(lead.customerCountry).toBe('US');
    });

    test('should extract complete vehicle information', async () => {
      const result = await parser.parseAdfXml(TEST_SCENARIOS[0].xml); // Valid complete ADF
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      expect(lead.vehicleYear).toBe(2024);
      expect(lead.vehicleMake).toBe('Honda');
      expect(lead.vehicleModel).toBe('Accord');
      expect(lead.vehicleTrim).toBe('Sport');
      expect(lead.vehicleVin).toBe('1HGCV1F30PA123456');
      expect(lead.vehicleStock).toBe('H24001');
      expect(lead.vehicleCondition).toBe('New');
      expect(lead.vehiclePrice).toBe(3250000); // Price in cents
      expect(lead.vehicleMileage).toBe(12);
    });

    test('should extract vendor and provider information', async () => {
      const result = await parser.parseAdfXml(TEST_SCENARIOS[0].xml); // Valid complete ADF
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      // Vendor information
      expect(lead.vendorName).toBe('Springfield Honda');
      expect(lead.vendorEmail).toBe('<EMAIL>');
      expect(lead.vendorPhone).toBe('******-987-6543');
      expect(lead.vendorAddress).toBe('456 Auto Plaza Drive');
      expect(lead.vendorCity).toBe('Springfield');
      expect(lead.vendorState).toBe('IL');
      expect(lead.vendorZip).toBe('62702');
      
      // Provider information
      expect(lead.providerName).toBe('AutoTrader.com');
      expect(lead.providerEmail).toBe('<EMAIL>');
      expect(lead.providerPhone).toBe('******-AUTOTRADER');
      expect(lead.providerService).toBe('AutoTrader');
    });

    test('should extract trade-in information', async () => {
      const result = await parser.parseAdfXml(TEST_SCENARIOS[0].xml); // Valid complete ADF
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      expect(lead.tradeInYear).toBe(2018);
      expect(lead.tradeInMake).toBe('Toyota');
      expect(lead.tradeInModel).toBe('Camry');
      expect(lead.tradeInTrim).toBe('LE');
      expect(lead.tradeInVin).toBe('4T1BF1FK5JU123456');
      expect(lead.tradeInMileage).toBe(45000);
      expect(lead.tradeInCondition).toBe('Good');
      expect(lead.tradeInValue).toBe(1850000); // Value in cents
    });

    test('should extract comments and timeframe', async () => {
      const result = await parser.parseAdfXml(TEST_SCENARIOS[0].xml); // Valid complete ADF
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      expect(lead.comments).toBe('Looking for a reliable sedan with good fuel economy. Interested in financing options.');
      expect(lead.timeFrame).toBe('Within 2 weeks');
    });
  });

  describe('Edge Cases', () => {
    test('should handle multiple phone numbers correctly', async () => {
      const result = await parser.parseAdfXml(EDGE_CASE_MULTIPLE_PHONES_ADF);
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      // Should extract the first voice phone
      expect(lead.customerPhone).toBe('************');
    });

    test('should handle very long content without truncation', async () => {
      const result = await parser.parseAdfXml(EDGE_CASE_LONG_CONTENT_ADF);
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      expect(lead.customerFirstName).toBe('VeryLongFirstNameThatExceedsNormalLimitsAndTestsFieldLengthValidation');
      expect(lead.customerLastName).toBe('VeryLongLastNameThatAlsoExceedsNormalLimitsForTestingPurposes');
      expect(lead.customerEmail).toBe('<EMAIL>');
      expect(lead.comments).toBeDefined();
      expect(lead.comments!.length).toBeGreaterThan(500); // Very long comment
    });

    test('should handle special characters and international content', async () => {
      const result = await parser.parseAdfXml(EDGE_CASE_SPECIAL_CHARS_ADF);
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      expect(lead.customerFirstName).toBe('José');
      expect(lead.customerLastName).toBe('García-Rodriguez');
      expect(lead.customerCity).toBe('São Paulo');
      expect(lead.customerState).toBe('SP');
      expect(lead.customerZip).toBe('01234-567');
      expect(lead.customerCountry).toBe('BR');
      expect(lead.comments).toContain('"premium"');
      expect(lead.comments).toContain('$25,000-$30,000');
    });

    test('should handle simple string name format', async () => {
      const result = await parser.parseAdfXml(TEST_SCENARIOS[3].xml); // Simple name format
      
      expect(result.success).toBe(true);
      const lead = result.mappedLead!;
      
      expect(lead.customerFullName).toBe('Sarah Wilson');
      expect(lead.customerFirstName).toBe('Sarah');
      expect(lead.customerLastName).toBe('Wilson');
    });
  });

  describe('Deduplication Hash Generation', () => {
    test('should generate consistent deduplication hashes for identical leads', async () => {
      const result1 = await parser.parseAdfXml(DUPLICATE_LEAD_SET[0]);
      const result2 = await parser.parseAdfXml(DUPLICATE_LEAD_SET[1]); // Exact duplicate
      
      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      
      expect(result1.mappedLead!.deduplicationHash).toBe(result2.mappedLead!.deduplicationHash);
    });

    test('should generate different hashes for different leads', async () => {
      const result1 = await parser.parseAdfXml(DUPLICATE_LEAD_SET[0]);
      const result2 = await parser.parseAdfXml(DUPLICATE_LEAD_SET[2]); // Different lead
      
      expect(result1.success).toBe(true);
      expect(result2.success).toBe(true);
      
      expect(result1.mappedLead!.deduplicationHash).not.toBe(result2.mappedLead!.deduplicationHash);
    });

    test('should include deduplication hash in all parsed leads', async () => {
      const result = await parser.parseAdfXml(TEST_SCENARIOS[0].xml);
      
      expect(result.success).toBe(true);
      expect(result.mappedLead!.deduplicationHash).toBeDefined();
      expect(result.mappedLead!.deduplicationHash).toHaveLength(64); // SHA-256 hash length
    });
  });
});

describe('ADF Schema Validator', () => {
  describe('Structure Validation', () => {
    test('should validate required ADF structure elements', () => {
      const validStructure = {
        adf: {
          $: { version: '1.0' },
          prospect: {
            requestdate: '2024-01-15T10:30:00Z',
            customer: {
              contact: {
                name: {
                  part: { $: { type: 'full' }, _: 'Test User' }
                }
              }
            }
          }
        }
      };

      const result = AdfSchemaValidator.validateAdfStructure(validStructure);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    test('should reject structure missing root ADF element', () => {
      const invalidStructure = {
        prospect: {
          requestdate: '2024-01-15T10:30:00Z'
        }
      };

      const result = AdfSchemaValidator.validateAdfStructure(invalidStructure);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing root <adf> element');
    });
  });
});