import { describe, it, expect, beforeEach, afterEach, jest } from '@jest/globals';
import { AdfLeadProcessor } from '../../server/services/adf-lead-processor';
import { db } from '../../shared/db';
import { adfLeads, adfProcessingLogs } from '../../shared/adf-schema';
import { eq } from 'drizzle-orm';
import { validAdfXmlSamples, invalidAdfXmlSamples, edgeCaseAdfXmlSamples, deduplicationTestData } from './sample-adf-data';

// Mock database
jest.mock('../../shared/db', () => ({
  db: {
    select: jest.fn(),
    insert: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    query: jest.fn(),
  }
}));

// Mock dealership finder
jest.mock('../../server/services/dealership-finder', () => ({
  findDealershipByEmail: jest.fn(),
  findDealershipByName: jest.fn()
}));

const mockDb = db as any;

describe('AdfLeadProcessor', () => {
  let processor: AdfLeadProcessor;

  beforeEach(() => {
    processor = new AdfLeadProcessor();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  describe('Lead Processing', () => {
    it('should successfully process valid ADF lead', async () => {
      const mockDealership = { id: 1, name: 'Test Dealership' };
      
      // Mock dealership lookup
      const { findDealershipByEmail } = require('../../server/services/dealership-finder');
      findDealershipByEmail.mockResolvedValue(mockDealership);

      // Mock database queries
      mockDb.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([]) // No duplicates
        })
      });

      mockDb.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockResolvedValue([{ id: 123, deduplicationHash: 'test-hash' }])
        })
      });

      const result = await processor.processAdfLead({
        xmlContent: validAdfXmlSamples.comprehensive,
        source: {
          messageId: 'test-123',
          from: '<EMAIL>',
          subject: 'New Lead',
          receivedAt: new Date()
        }
      });

      expect(result.success).toBe(true);
      expect(result.leadId).toBe(123);
      expect(result.errors).toHaveLength(0);
      expect(mockDb.insert).toHaveBeenCalledWith(adfLeads);
    });

    it('should handle invalid ADF XML', async () => {
      const result = await processor.processAdfLead({
        xmlContent: invalidAdfXmlSamples.malformedXml,
        source: {
          messageId: 'test-invalid',
          from: '<EMAIL>',
          subject: 'Invalid Lead',
          receivedAt: new Date()
        }
      });

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toContain('XML parsing failed');
    });

    it('should detect duplicate leads', async () => {
      const mockDealership = { id: 1, name: 'Test Dealership' };
      
      const { findDealershipByEmail } = require('../../server/services/dealership-finder');
      findDealershipByEmail.mockResolvedValue(mockDealership);

      // Mock existing lead found
      mockDb.select.mockReturnValue({
        from: jest.fn().mockReturnValue({
          where: jest.fn().mockResolvedValue([
            { id: 456, deduplicationHash: 'existing-hash', createdAt: new Date() }
          ])
        })
      });

      const result = await processor.processAdfLead({
        xmlContent: deduplicationTestData.duplicate1.xml,
        source: {
          messageId: 'test-duplicate',
          from: '<EMAIL>',
          subject: 'Duplicate Lead',
          receivedAt: new Date()
        }
      });

      expect(result.success).toBe(false);
      expect(result.errors[0]).toContain('Duplicate lead detected');
      expect(result.duplicateLeadId).toBe(456);
    });
  });

  describe('Error Handling', () => {
    it('should log processing errors', async () => {
      mockDb.insert.mockReturnValue({
        values: jest.fn().mockReturnValue({
          returning: jest.fn().mockRejectedValue(new Error('Database error'))
        })
      });

      const result = await processor.processAdfLead({
        xmlContent: validAdfXmlSamples.minimal,
        source: {
          messageId: 'test-error',
          from: '<EMAIL>',
          subject: 'Error Lead',
          receivedAt: new Date()
        }
      });

      expect(result.success).toBe(false);
      expect(result.errors[0]).toContain('Database error');
    });
  });
});