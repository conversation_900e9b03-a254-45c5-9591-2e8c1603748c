import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { TwilioSMSService, SMSMessage } from '../../server/services/twilio-sms-service';
import { credentialsService } from '../../server/services/credentials-service';
import { Twilio } from 'twilio';

// Mock Twilio
vi.mock('twilio');
const MockedTwilio = vi.mocked(Twilio);

// Mock credentials service
vi.mock('../../server/services/credentials-service');
const mockedCredentialsService = vi.mocked(credentialsService);

// Mock database
vi.mock('../../server/db', () => ({
  default: {
    execute: vi.fn()
  }
}));

// Mock logger
vi.mock('../../server/utils/logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn()
  }
}));

describe('TwilioSMSService', () => {
  let smsService: TwilioSMSService;
  let mockTwilioClient: any;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Setup mock Twilio client
    mockTwilioClient = {
      messages: {
        create: vi.fn()
      }
    };
    
    MockedTwilio.mockImplementation(() => mockTwilioClient);
    
    // Setup mock credentials
    mockedCredentialsService.getTwilioCredentials.mockResolvedValue({
      accountSid: 'test_account_sid',
      authToken: 'test_auth_token',
      fromNumber: '+***********',
      webhookUrl: 'https://example.com/webhook'
    });

    smsService = new TwilioSMSService();
  });

  afterEach(() => {
    vi.resetAllMocks();
  });

  describe('sendSMS', () => {
    it('should send SMS successfully', async () => {
      const mockMessage = {
        sid: 'test_message_sid',
        status: 'queued'
      };

      mockTwilioClient.messages.create.mockResolvedValue(mockMessage);

      const smsMessage: SMSMessage = {
        dealershipId: 1,
        toPhone: '+***********',
        message: 'Test message',
        metadata: { testKey: 'testValue' }
      };

      const result = await smsService.sendSMS(smsMessage);

      expect(result.success).toBe(true);
      expect(result.messageSid).toBe('test_message_sid');
      expect(mockTwilioClient.messages.create).toHaveBeenCalledWith({
        body: 'Test message',
        from: '+***********',
        to: '+***********',
        statusCallback: `${process.env.BASE_URL}/api/webhooks/twilio/status`
      });
    });

    it('should handle invalid phone numbers', async () => {
      const smsMessage: SMSMessage = {
        dealershipId: 1,
        toPhone: 'invalid-phone',
        message: 'Test message'
      };

      const result = await smsService.sendSMS(smsMessage);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid phone number format');
    });

    it('should handle Twilio API errors', async () => {
      mockTwilioClient.messages.create.mockRejectedValue(new Error('Twilio API error'));

      const smsMessage: SMSMessage = {
        dealershipId: 1,
        toPhone: '+***********',
        message: 'Test message'
      };

      const result = await smsService.sendSMS(smsMessage);

      expect(result.success).toBe(false);
      expect(result.error).toBe('Twilio API error');
    });

    it('should check opt-out status before sending', async () => {
      // This would require mocking the database query for opt-out check
      // For now, we'll just verify that sendSMS handles the flow
      const smsMessage: SMSMessage = {
        dealershipId: 1,
        toPhone: '+***********',
        message: 'Test message'
      };

      mockTwilioClient.messages.create.mockResolvedValue({
        sid: 'test_sid',
        status: 'queued'
      });

      await smsService.sendSMS(smsMessage);

      expect(mockTwilioClient.messages.create).toHaveBeenCalled();
    });
  });

  describe('processWebhook', () => {
    it('should process delivery status webhook successfully', async () => {
      const webhookData = {
        MessageSid: 'test_message_sid',
        MessageStatus: 'delivered',
        ErrorCode: null,
        ErrorMessage: null
      };

      await expect(smsService.processWebhook(webhookData)).resolves.not.toThrow();
    });

    it('should handle webhook with error status', async () => {
      const webhookData = {
        MessageSid: 'test_message_sid',
        MessageStatus: 'failed',
        ErrorCode: '30008',
        ErrorMessage: 'Unknown error'
      };

      await expect(smsService.processWebhook(webhookData)).resolves.not.toThrow();
    });

    it('should throw error for invalid webhook data', async () => {
      const webhookData = {
        // Missing required fields
      };

      await expect(smsService.processWebhook(webhookData)).rejects.toThrow();
    });
  });

  describe('handleOptOut', () => {
    it('should handle opt-out request successfully', async () => {
      await expect(
        smsService.handleOptOut(1, '+***********', 'user_request')
      ).resolves.not.toThrow();
    });

    it('should handle invalid phone number in opt-out', async () => {
      await expect(
        smsService.handleOptOut(1, 'invalid-phone', 'user_request')
      ).rejects.toThrow('Invalid phone number format');
    });
  });

  describe('maskPhoneNumber', () => {
    it('should mask phone number correctly with default options', () => {
      const phone = '+***********';
      const masked = smsService.maskPhoneNumber(phone);
      
      expect(masked).toMatch(/\*+4567$/);
      expect(masked.length).toBe(phone.replace(/\D/g, '').length);
    });

    it('should mask phone number with custom options', () => {
      const phone = '+***********';
      const masked = smsService.maskPhoneNumber(phone, {
        visibleDigits: 2,
        maskCharacter: 'X'
      });
      
      expect(masked).toMatch(/X+67$/);
    });

    it('should handle short phone numbers', () => {
      const phone = '123';
      const masked = smsService.maskPhoneNumber(phone);
      
      expect(masked).toBe('**********'); // Default masked phone length
    });
  });

  describe('normalizePhoneNumber', () => {
    it('should normalize US 10-digit number', () => {
      const normalized = (smsService as any).normalizePhoneNumber('5551234567');
      expect(normalized).toBe('+***********');
    });

    it('should normalize US 11-digit number', () => {
      const normalized = (smsService as any).normalizePhoneNumber('***********');
      expect(normalized).toBe('+***********');
    });

    it('should handle already formatted number', () => {
      const normalized = (smsService as any).normalizePhoneNumber('+***********');
      expect(normalized).toBe('+***********');
    });

    it('should return null for invalid format', () => {
      const normalized = (smsService as any).normalizePhoneNumber('123');
      expect(normalized).toBeNull();
    });
  });

  describe('retryFailedMessages', () => {
    it('should retry failed messages within retry limit', async () => {
      // This would require more extensive mocking of database queries
      // For now, we'll just verify the method doesn't throw
      await expect(smsService.retryFailedMessages()).resolves.not.toThrow();
    });
  });
});

describe('SMS Integration Tests', () => {
  // These tests would run against a test Twilio account or sandbox
  describe('Real Twilio Integration', () => {
    it.skip('should send real SMS via Twilio sandbox', async () => {
      // This test would require real Twilio credentials and would be skipped in CI
      // Only run during manual testing with proper test credentials
      
      const smsService = new TwilioSMSService();
      const testMessage: SMSMessage = {
        dealershipId: 1,
        toPhone: process.env.TEST_PHONE_NUMBER || '+***********',
        message: 'Test message from Rylie AI - Integration Test'
      };

      const result = await smsService.sendSMS(testMessage);
      expect(result.success).toBe(true);
      expect(result.messageSid).toBeDefined();
    });

    it.skip('should validate webhook signature', async () => {
      // This test would validate actual Twilio webhook signatures
      // Implementation would depend on Twilio's webhook validation library
    });
  });
});

describe('SMS Performance Tests', () => {
  it('should handle high volume of SMS requests', async () => {
    const smsService = new TwilioSMSService();
    
    // Mock successful responses
    mockTwilioClient.messages.create.mockResolvedValue({
      sid: 'test_sid',
      status: 'queued'
    });

    // Send 100 messages concurrently
    const promises = Array.from({ length: 100 }, (_, i) => {
      const message: SMSMessage = {
        dealershipId: 1,
        toPhone: `+1555123${String(i).padStart(4, '0')}`,
        message: `Test message ${i}`
      };
      return smsService.sendSMS(message);
    });

    const results = await Promise.all(promises);
    
    // All should succeed
    expect(results.every(r => r.success)).toBe(true);
    expect(mockTwilioClient.messages.create).toHaveBeenCalledTimes(100);
  });

  it('should handle rate limiting gracefully', async () => {
    const smsService = new TwilioSMSService();
    
    // Mock rate limiting error
    mockTwilioClient.messages.create.mockRejectedValue(
      new Error('Rate limit exceeded')
    );

    const message: SMSMessage = {
      dealershipId: 1,
      toPhone: '+***********',
      message: 'Test message'
    };

    const result = await smsService.sendSMS(message);
    
    expect(result.success).toBe(false);
    expect(result.error).toContain('Rate limit exceeded');
  });
});

describe('SMS Security Tests', () => {
  it('should properly mask phone numbers in logs', () => {
    const smsService = new TwilioSMSService();
    const phone = '+***********';
    const masked = smsService.maskPhoneNumber(phone);
    
    expect(masked).not.toContain('555123');
    expect(masked).toContain('4567'); // Should show last 4 digits
  });

  it('should validate phone number format before sending', async () => {
    const smsService = new TwilioSMSService();
    
    const invalidNumbers = [
      'not-a-phone',
      '123',
      '******-INVALID',
      '************-ext-123'
    ];

    for (const invalidNumber of invalidNumbers) {
      const message: SMSMessage = {
        dealershipId: 1,
        toPhone: invalidNumber,
        message: 'Test message'
      };

      const result = await smsService.sendSMS(message);
      expect(result.success).toBe(false);
    }
  });

  it('should not expose sensitive data in error messages', async () => {
    const smsService = new TwilioSMSService();
    
    mockTwilioClient.messages.create.mockRejectedValue(
      new Error('Authentication failed: invalid credentials')
    );

    const message: SMSMessage = {
      dealershipId: 1,
      toPhone: '+***********',
      message: 'Test message'
    };

    const result = await smsService.sendSMS(message);
    
    expect(result.success).toBe(false);
    // Error should not expose credentials or other sensitive info
    expect(result.error).not.toContain('account_sid');
    expect(result.error).not.toContain('auth_token');
  });
});