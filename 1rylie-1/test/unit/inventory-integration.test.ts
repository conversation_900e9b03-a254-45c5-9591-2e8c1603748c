import { describe, it, expect, beforeEach, afterEach, vi, Mock } from 'vitest';
import { generateAIResponse } from '../../server/services/openai';
import { enhancedConversationService } from '../../server/services/enhanced-conversation-service';
import db from '../../server/db';

// Mock the database
vi.mock('../../server/db', () => ({
  default: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
    delete: vi.fn(),
  }
}));

// Mock the OpenAI service
vi.mock('openai', () => ({
  default: vi.fn().mockImplementation(() => ({
    chat: {
      completions: {
        create: vi.fn()
      }
    }
  }))
}));

// Mock logger
vi.mock('../../server/utils/logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
    debug: vi.fn()
  }
}));

describe('Inventory Integration', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('generateAIResponse with inventory context', () => {
    it('should include inventory context when vehicle keywords are detected', async () => {
      // Mock database response for vehicle search
      const mockVehicles = [
        {
          id: 1,
          vin: 'TEST123456789',
          make: 'Toyota',
          model: 'Camry',
          year: 2023,
          trim: 'LE',
          salePrice: 2500000, // $25,000 in cents
          stockNumber: 'TC001',
          dealershipId: 1
        }
      ];

      const mockDbSelect = vi.fn().mockReturnValue({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue(mockVehicles)
          })
        })
      });

      (db.select as Mock).mockImplementation(mockDbSelect);

      // Mock OpenAI response
      const mockOpenAIResponse = {
        choices: [{
          message: {
            content: JSON.stringify({
              answer: "I'd be happy to help you with Toyota Camry options! We currently have a 2023 Toyota Camry LE available for $25,000."
            })
          }
        }]
      };

      // Test the enhanced AI response
      const result = await generateAIResponse(
        "You are a helpful car salesperson.",
        "I'm interested in a Toyota Camry",
        1 // dealershipId
      );

      expect(mockDbSelect).toHaveBeenCalled();
      expect(result).toContain('Toyota Camry');
    });

    it('should not include inventory context when no vehicle keywords are detected', async () => {
      const mockDbSelect = vi.fn();
      (db.select as Mock).mockImplementation(mockDbSelect);

      await generateAIResponse(
        "You are a helpful assistant.",
        "What's the weather like today?",
        1
      );

      // Should not have queried the database for vehicles
      expect(mockDbSelect).not.toHaveBeenCalled();
    });

    it('should handle conversation history correctly', async () => {
      const conversationHistory = [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi! How can I help you today?' }
      ];

      const result = await generateAIResponse(
        "You are a helpful car salesperson.",
        "I need more information about financing",
        undefined,
        conversationHistory
      );

      // Should include conversation history in the prompt
      expect(result).toBeDefined();
    });
  });

  describe('Vehicle keyword detection', () => {
    // We'll need to import the helper functions to test them individually
    // For now, we'll test the integration through the main function

    it('should detect vehicle makes correctly', async () => {
      const testCases = [
        'I want a Toyota',
        'Looking for Honda options',
        'Do you have any Ford trucks?',
        'Interested in Chevy Silverado'
      ];

      for (const testCase of testCases) {
        const mockDbSelect = vi.fn().mockReturnValue({
          from: vi.fn().mockReturnValue({
            where: vi.fn().mockReturnValue({
              limit: vi.fn().mockResolvedValue([])
            })
          })
        });

        (db.select as Mock).mockImplementation(mockDbSelect);

        await generateAIResponse(
          "You are a helpful car salesperson.",
          testCase,
          1
        );

        expect(mockDbSelect).toHaveBeenCalled();
      }
    });

    it('should detect vehicle types correctly', async () => {
      const testCases = [
        'I need an SUV',
        'Looking for a sedan',
        'Do you have trucks?',
        'Want a coupe'
      ];

      for (const testCase of testCases) {
        const mockDbSelect = vi.fn().mockReturnValue({
          from: vi.fn().mockReturnValue({
            where: vi.fn().mockReturnValue({
              limit: vi.fn().mockResolvedValue([])
            })
          })
        });

        (db.select as Mock).mockImplementation(mockDbSelect);

        await generateAIResponse(
          "You are a helpful car salesperson.",
          testCase,
          1
        );

        expect(mockDbSelect).toHaveBeenCalled();
      }
    });

    it('should detect year specifications', async () => {
      const testCases = [
        'I want a 2023 model',
        'Looking for 2022 cars',
        'Do you have 2024 vehicles?'
      ];

      for (const testCase of testCases) {
        const mockDbSelect = vi.fn().mockReturnValue({
          from: vi.fn().mockReturnValue({
            where: vi.fn().mockReturnValue({
              limit: vi.fn().mockResolvedValue([])
            })
          })
        });

        (db.select as Mock).mockImplementation(mockDbSelect);

        await generateAIResponse(
          "You are a helpful car salesperson.",
          testCase,
          1
        );

        expect(mockDbSelect).toHaveBeenCalled();
      }
    });
  });

  describe('Enhanced Conversation Service', () => {
    it('should generate AI response with conversation context', async () => {
      // Mock conversation context retrieval
      const mockConversationContext = {
        messages: [
          { role: 'user', content: 'Hello', timestamp: new Date() },
          { role: 'assistant', content: 'Hi there!', timestamp: new Date() }
        ],
        persona: {
          name: 'Rylie',
          promptTemplate: 'You are Rylie, a helpful car sales agent.',
          arguments: { dealershipName: 'Test Motors' }
        }
      };

      // Mock the private method by testing the public interface
      const options = {
        conversationId: 'test-conv-id',
        dealershipId: 1,
        content: 'I want to buy a car',
        sender: 'customer' as const,
        includeInventoryContext: true,
        useConversationHistory: true
      };

      // Test would require actual implementation with proper mocking
      // This is a placeholder for the test structure
      expect(enhancedConversationService).toBeDefined();
    });

    it('should format conversation history correctly', async () => {
      const messages = [
        { role: 'user', content: 'Hi', timestamp: new Date() },
        { role: 'assistant', content: 'Hello!', timestamp: new Date() },
        { role: 'user', content: 'I need help', timestamp: new Date() }
      ];

      // Test the conversation history formatting
      // This would test the private formatConversationHistory method
      expect(messages).toHaveLength(3);
    });

    it('should process prompt templates with variables', async () => {
      const template = 'Hello {{customerName}}, welcome to {{dealershipName}}!';
      const variables = {
        customerName: 'John',
        dealershipName: 'Test Motors'
      };

      const expected = 'Hello John, welcome to Test Motors!';
      
      // Test template processing
      let processed = template;
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        processed = processed.replace(regex, String(value));
      });

      expect(processed).toBe(expected);
    });
  });

  describe('Inventory Context Formatting', () => {
    it('should format vehicle information correctly', () => {
      const vehicles = [
        {
          id: 1,
          year: 2023,
          make: 'Toyota',
          model: 'Camry',
          trim: 'LE',
          salePrice: 2500000, // $25,000 in cents
          stockNumber: 'TC001',
          dealershipId: 1
        },
        {
          id: 2,
          year: 2022,
          make: 'Honda',
          model: 'Accord',
          trim: 'Sport',
          salePrice: 2800000, // $28,000 in cents
          stockNumber: 'HA002',
          dealershipId: 1
        }
      ];

      // Test inventory context formatting
      const vehicleDescriptions = vehicles.map(vehicle => {
        const price = vehicle.salePrice;
        const priceStr = price ? `$${(price / 100).toLocaleString()}` : 'Price available upon request';
        
        return `${vehicle.year} ${vehicle.make} ${vehicle.model} ${vehicle.trim || ''} - ${priceStr} (Stock #${vehicle.stockNumber || 'N/A'})`;
      }).join(', ');

      const expected = '2023 Toyota Camry LE - $25,000 (Stock #TC001), 2022 Honda Accord Sport - $28,000 (Stock #HA002)';
      expect(vehicleDescriptions).toBe(expected);

      const contextMessage = `(Context: The dealership currently has ${vehicles.length} matching vehicle(s) in stock: ${vehicleDescriptions})`;
      expect(contextMessage).toContain('2 matching vehicle(s) in stock');
    });

    it('should handle vehicles without pricing information', () => {
      const vehicle = {
        id: 1,
        year: 2023,
        make: 'Toyota',
        model: 'Camry',
        trim: null,
        salePrice: null,
        msrp: null,
        stockNumber: 'TC001',
        dealershipId: 1
      };

      const priceStr = vehicle.salePrice || vehicle.msrp 
        ? `$${((vehicle.salePrice || vehicle.msrp)! / 100).toLocaleString()}` 
        : 'Price available upon request';

      expect(priceStr).toBe('Price available upon request');
    });
  });

  describe('Search Term Extraction', () => {
    it('should extract vehicle makes from messages', () => {
      const testCases = [
        { message: 'I want a Toyota Camry', expected: 'Toyota' },
        { message: 'Looking for Honda options', expected: 'Honda' },
        { message: 'Do you have Ford trucks?', expected: 'Ford' },
        { message: 'Chevy or Chevrolet please', expected: 'Chevrolet' } // Should map chevy to Chevrolet
      ];

      const makeMap: Record<string, string> = {
        'toyota': 'Toyota',
        'honda': 'Honda',
        'ford': 'Ford',
        'chevrolet': 'Chevrolet',
        'chevy': 'Chevrolet'
      };

      testCases.forEach(({ message, expected }) => {
        const lowerMessage = message.toLowerCase();
        let extractedMake: string | undefined;

        for (const [key, value] of Object.entries(makeMap)) {
          if (lowerMessage.includes(key)) {
            extractedMake = value;
            break;
          }
        }

        expect(extractedMake).toBe(expected);
      });
    });

    it('should extract years from messages', () => {
      const testCases = [
        { message: 'I want a 2023 model', expected: 2023 },
        { message: 'Looking for 2022 cars', expected: 2022 },
        { message: '2024 vehicles preferred', expected: 2024 },
        { message: 'Something from 1995', expected: 1995 },
        { message: 'Year 2030 would be nice', expected: null } // Future year, should be rejected
      ];

      testCases.forEach(({ message, expected }) => {
        const currentYear = new Date().getFullYear();
        const yearMatch = message.match(/\b(19[9]\d|20[0-2]\d)\b/);
        let extractedYear: number | null = null;

        if (yearMatch) {
          const year = parseInt(yearMatch[0]);
          if (year >= 1990 && year <= currentYear + 1) {
            extractedYear = year;
          }
        }

        expect(extractedYear).toBe(expected);
      });
    });

    it('should extract body styles from messages', () => {
      const testCases = [
        { message: 'I need an SUV', expected: 'SUV' },
        { message: 'Looking for sedan', expected: 'Sedan' },
        { message: 'Want a truck please', expected: 'Truck' },
        { message: 'Coupe would be nice', expected: 'Coupe' }
      ];

      const bodyStyleMap: Record<string, string> = {
        'suv': 'SUV',
        'sedan': 'Sedan',
        'truck': 'Truck',
        'coupe': 'Coupe',
        'convertible': 'Convertible',
        'wagon': 'Wagon',
        'hatchback': 'Hatchback'
      };

      testCases.forEach(({ message, expected }) => {
        const lowerMessage = message.toLowerCase();
        let extractedBodyStyle: string | undefined;

        for (const [key, value] of Object.entries(bodyStyleMap)) {
          if (lowerMessage.includes(key)) {
            extractedBodyStyle = value;
            break;
          }
        }

        expect(extractedBodyStyle).toBe(expected);
      });
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors gracefully', async () => {
      const mockDbSelect = vi.fn().mockReturnValue({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockRejectedValue(new Error('Database connection failed'))
          })
        })
      });

      (db.select as Mock).mockImplementation(mockDbSelect);

      // Should not throw an error, should return empty results
      const result = await generateAIResponse(
        "You are a helpful car salesperson.",
        "I want a Toyota Camry",
        1
      );

      expect(result).toBeDefined();
    });

    it('should handle empty inventory gracefully', async () => {
      const mockDbSelect = vi.fn().mockReturnValue({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([]) // Empty inventory
          })
        })
      });

      (db.select as Mock).mockImplementation(mockDbSelect);

      const result = await generateAIResponse(
        "You are a helpful car salesperson.",
        "I want a Toyota Camry",
        1
      );

      expect(result).toBeDefined();
    });

    it('should handle malformed vehicle data', async () => {
      const malformedVehicle = {
        id: 1,
        // Missing required fields
        vin: null,
        make: '',
        model: undefined,
        year: 'invalid',
        salePrice: 'not-a-number'
      };

      const mockDbSelect = vi.fn().mockReturnValue({
        from: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([malformedVehicle])
          })
        })
      });

      (db.select as Mock).mockImplementation(mockDbSelect);

      const result = await generateAIResponse(
        "You are a helpful car salesperson.",
        "I want a car",
        1
      );

      expect(result).toBeDefined();
    });
  });
});