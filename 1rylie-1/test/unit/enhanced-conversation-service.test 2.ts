import { describe, it, expect, beforeEach, vi, Mock } from 'vitest';
import { EnhancedConversationService } from '../../server/services/enhanced-conversation-service';
import db from '../../server/db';

// Mock dependencies
vi.mock('../../server/db', () => ({
  default: {
    select: vi.fn(),
    insert: vi.fn(),
    update: vi.fn(),
  }
}));

vi.mock('../../server/services/openai', () => ({
  generateAIResponse: vi.fn()
}));

vi.mock('../../server/utils/logger', () => ({
  default: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  }
}));

describe('EnhancedConversationService', () => {
  let service: EnhancedConversationService;

  beforeEach(() => {
    service = new EnhancedConversationService();
    vi.clearAllMocks();
  });

  describe('generateAIResponseWithContext', () => {
    it('should generate AI response with conversation history', async () => {
      // Mock conversation context
      const mockMessages = [
        { role: 'user', content: 'Hello', timestamp: new Date() },
        { role: 'assistant', content: 'Hi there!', timestamp: new Date() }
      ];

      const mockPersona = {
        id: 1,
        name: 'Rylie',
        promptTemplate: 'You are {{agentName}}, a helpful car sales agent.',
        arguments: { agentName: 'Rylie' }
      };

      // Mock database responses
      const mockConversationSelect = vi.fn().mockReturnValue({
        leftJoin: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([{
              id: 'conv-1',
              aiPersonaId: 1,
              persona: mockPersona
            }])
          })
        })
      });

      const mockMessagesSelect = vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          orderBy: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue(mockMessages.map(msg => ({
              content: msg.content,
              sender: msg.role === 'user' ? 'customer' : 'ai',
              createdAt: msg.timestamp
            })))
          })
        })
      });

      (db.select as Mock)
        .mockImplementationOnce(mockConversationSelect)
        .mockImplementationOnce(mockMessagesSelect);

      const options = {
        conversationId: 'conv-1',
        dealershipId: 1,
        content: 'I want to buy a car',
        sender: 'ai' as const,
        useConversationHistory: true,
        includeInventoryContext: false
      };

      const result = await service.generateAIResponseWithContext(options);

      expect(result).toBeDefined();
      expect(result.context).toBeDefined();
      expect(result.context.messages).toHaveLength(2);
      expect(result.context.persona).toBeDefined();
    });

    it('should handle missing conversation gracefully', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        leftJoin: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([]) // No conversation found
          })
        })
      });

      (db.select as Mock).mockImplementation(mockSelect);

      const options = {
        conversationId: 'non-existent',
        dealershipId: 1,
        content: 'Hello',
        sender: 'ai' as const
      };

      await expect(service.generateAIResponseWithContext(options))
        .rejects.toThrow('Conversation not found');
    });
  });

  describe('sendEnhancedReply', () => {
    it('should generate AI response when sender is ai', async () => {
      // Mock the conversation context and AI generation
      const mockContext = {
        messages: [],
        persona: {
          name: 'Rylie',
          promptTemplate: 'You are a helpful assistant.',
          arguments: {}
        }
      };

      // Mock generateAIResponseWithContext
      const mockGenerateResponse = vi.spyOn(service, 'generateAIResponseWithContext')
        .mockResolvedValue({
          response: 'Hello! How can I help you today?',
          context: mockContext
        });

      // Mock sendReply
      const mockSendReply = vi.spyOn(service, 'sendReply')
        .mockResolvedValue({
          success: true,
          messageId: 'msg-1',
          conversationId: 'conv-1',
          timestamp: new Date(),
          errors: []
        });

      const options = {
        conversationId: 'conv-1',
        dealershipId: 1,
        content: 'Generate response',
        sender: 'ai' as const
      };

      const result = await service.sendEnhancedReply(options);

      expect(mockGenerateResponse).toHaveBeenCalledWith(options);
      expect(mockSendReply).toHaveBeenCalledWith(1, {
        conversationId: 'conv-1',
        content: 'Hello! How can I help you today?',
        sender: 'ai',
        senderUserId: undefined,
        senderName: 'Rylie'
      });
      expect(result.success).toBe(true);
    });

    it('should send user message directly when sender is not ai', async () => {
      const mockSendReply = vi.spyOn(service, 'sendReply')
        .mockResolvedValue({
          success: true,
          messageId: 'msg-1',
          conversationId: 'conv-1',
          timestamp: new Date(),
          errors: []
        });

      const options = {
        conversationId: 'conv-1',
        dealershipId: 1,
        content: 'Hello from customer',
        sender: 'customer' as const,
        senderUserId: 123
      };

      const result = await service.sendEnhancedReply(options);

      expect(mockSendReply).toHaveBeenCalledWith(1, {
        conversationId: 'conv-1',
        content: 'Hello from customer',
        sender: 'customer',
        senderUserId: 123
      });
      expect(result.success).toBe(true);
    });
  });

  describe('getConversationSummary', () => {
    it('should generate conversation summary from messages', async () => {
      const mockMessages = [
        { role: 'user', content: 'I want to buy a car', timestamp: new Date() },
        { role: 'assistant', content: 'Great! What type are you looking for?', timestamp: new Date() },
        { role: 'user', content: 'An SUV would be perfect', timestamp: new Date() }
      ];

      // Mock database responses
      const mockConversationSelect = vi.fn().mockReturnValue({
        leftJoin: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([{
              id: 'conv-1',
              aiPersonaId: null,
              persona: null
            }])
          })
        })
      });

      const mockMessagesSelect = vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          orderBy: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue(mockMessages.map(msg => ({
              content: msg.content,
              sender: msg.role === 'user' ? 'customer' : 'ai',
              createdAt: msg.timestamp
            })))
          })
        })
      });

      (db.select as Mock)
        .mockImplementationOnce(mockConversationSelect)
        .mockImplementationOnce(mockMessagesSelect);

      const summary = await service.getConversationSummary('conv-1', 1);

      expect(summary).toContain('Conversation Summary:');
      expect(summary).toContain('Customer: I want to buy a car');
      expect(summary).toContain('Agent: Great! What type are you looking for?');
      expect(summary).toContain('Customer: An SUV would be perfect');
    });

    it('should handle empty conversation', async () => {
      const mockConversationSelect = vi.fn().mockReturnValue({
        leftJoin: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([{
              id: 'conv-1',
              aiPersonaId: null,
              persona: null
            }])
          })
        })
      });

      const mockMessagesSelect = vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          orderBy: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([]) // No messages
          })
        })
      });

      (db.select as Mock)
        .mockImplementationOnce(mockConversationSelect)
        .mockImplementationOnce(mockMessagesSelect);

      const summary = await service.getConversationSummary('conv-1', 1);

      expect(summary).toBe('No conversation history available.');
    });
  });

  describe('Template Processing', () => {
    it('should process prompt template with variables', () => {
      const template = 'Hello {{customerName}}, welcome to {{dealershipName}}! How can {{agentName}} help you today?';
      const args = {
        customerName: 'John',
        dealershipName: 'Best Motors',
        agentName: 'Rylie'
      };

      // Simulate the processPromptTemplate method
      let processedTemplate = template;
      Object.entries(args).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        processedTemplate = processedTemplate.replace(regex, String(value));
      });

      expect(processedTemplate).toBe('Hello John, welcome to Best Motors! How can Rylie help you today?');
    });

    it('should handle missing variables in template', () => {
      const template = 'Hello {{customerName}}, welcome to {{dealershipName}}!';
      const args = {
        customerName: 'John'
        // Missing dealershipName
      };

      let processedTemplate = template;
      Object.entries(args).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        processedTemplate = processedTemplate.replace(regex, String(value));
      });

      expect(processedTemplate).toBe('Hello John, welcome to {{dealershipName}}!');
      expect(processedTemplate).toContain('{{dealershipName}}'); // Should remain unprocessed
    });

    it('should handle extra spaces in template variables', () => {
      const template = 'Hello {{ customerName }}, welcome to {{  dealershipName  }}!';
      const args = {
        customerName: 'John',
        dealershipName: 'Best Motors'
      };

      let processedTemplate = template;
      Object.entries(args).forEach(([key, value]) => {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        processedTemplate = processedTemplate.replace(regex, String(value));
      });

      expect(processedTemplate).toBe('Hello John, welcome to Best Motors!');
    });
  });

  describe('Message Sender Mapping', () => {
    it('should map senders to correct AI roles', () => {
      const testCases = [
        { sender: 'customer', expected: 'user' },
        { sender: 'ai', expected: 'assistant' },
        { sender: 'agent', expected: 'assistant' },
        { sender: 'system', expected: 'system' },
        { sender: 'unknown', expected: 'user' } // Default fallback
      ];

      // Simulate the mapSenderToRole method
      const mapSenderToRole = (sender: string): string => {
        switch (sender) {
          case 'customer':
            return 'user';
          case 'ai':
            return 'assistant';
          case 'agent':
            return 'assistant';
          case 'system':
            return 'system';
          default:
            return 'user';
        }
      };

      testCases.forEach(({ sender, expected }) => {
        expect(mapSenderToRole(sender)).toBe(expected);
      });
    });
  });

  describe('Conversation History Formatting', () => {
    it('should format conversation history for AI context', () => {
      const messages = [
        { role: 'user', content: 'Hello', timestamp: new Date() },
        { role: 'assistant', content: 'Hi there!', timestamp: new Date() },
        { role: 'user', content: 'I need help', timestamp: new Date() }
      ];

      // Simulate the formatConversationHistory method
      const formatted = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      expect(formatted).toHaveLength(3);
      expect(formatted[0]).toEqual({ role: 'user', content: 'Hello' });
      expect(formatted[1]).toEqual({ role: 'assistant', content: 'Hi there!' });
      expect(formatted[2]).toEqual({ role: 'user', content: 'I need help' });
    });

    it('should limit conversation history to prevent token overflow', () => {
      const longHistory = Array.from({ length: 20 }, (_, i) => ({
        role: i % 2 === 0 ? 'user' : 'assistant',
        content: `Message ${i + 1}`,
        timestamp: new Date()
      }));

      // Simulate limiting to last 6 messages
      const recentHistory = longHistory.slice(-6);

      expect(recentHistory).toHaveLength(6);
      expect(recentHistory[0].content).toBe('Message 15');
      expect(recentHistory[5].content).toBe('Message 20');
    });
  });

  describe('Error Handling', () => {
    it('should handle database errors in context retrieval', async () => {
      const mockSelect = vi.fn().mockReturnValue({
        leftJoin: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockRejectedValue(new Error('Database connection failed'))
          })
        })
      });

      (db.select as Mock).mockImplementation(mockSelect);

      const options = {
        conversationId: 'conv-1',
        dealershipId: 1,
        content: 'Hello',
        sender: 'ai' as const
      };

      await expect(service.generateAIResponseWithContext(options))
        .rejects.toThrow();
    });

    it('should handle AI generation errors gracefully', async () => {
      // Mock successful context retrieval but failed AI generation
      const mockContext = {
        messages: [],
        persona: { name: 'Rylie', promptTemplate: 'Test prompt', arguments: {} }
      };

      // Mock database responses
      const mockConversationSelect = vi.fn().mockReturnValue({
        leftJoin: vi.fn().mockReturnValue({
          where: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([{
              id: 'conv-1',
              aiPersonaId: 1,
              persona: mockContext.persona
            }])
          })
        })
      });

      const mockMessagesSelect = vi.fn().mockReturnValue({
        where: vi.fn().mockReturnValue({
          orderBy: vi.fn().mockReturnValue({
            limit: vi.fn().mockResolvedValue([])
          })
        })
      });

      (db.select as Mock)
        .mockImplementationOnce(mockConversationSelect)
        .mockImplementationOnce(mockMessagesSelect);

      // Mock AI generation failure
      const { generateAIResponse } = await import('../../server/services/openai');
      (generateAIResponse as Mock).mockRejectedValue(new Error('AI service unavailable'));

      const options = {
        conversationId: 'conv-1',
        dealershipId: 1,
        content: 'Hello',
        sender: 'ai' as const
      };

      await expect(service.generateAIResponseWithContext(options))
        .rejects.toThrow();
    });
  });
});