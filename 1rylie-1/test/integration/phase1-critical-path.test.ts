/**
 * Critical Path Integration Tests for Phase 1 Production Readiness
 * 
 * These tests verify the essential flows work correctly:
 * 1. Lead ingestion → AI response generation
 * 2. Escalation trigger detection → handover creation
 * 3. Opt-out handling in SMS webhooks
 * 4. OpenAI error resilience and fallback
 * 5. Inventory lifecycle management
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import request from 'supertest';
import { ConversationService } from '../../server/services/conversation-service';
import { HandoverService } from '../../server/services/handover-service';
import { aiResponseService } from '../../server/services/ai-response-service';
import { evaluateEscalationTriggers, createEscalationTrigger } from '../../server/services/escalation-triggers';
import { processTsvInventory, cleanupStaleInventory } from '../../server/services/inventory-import';
import { twilioSMSService } from '../../server/services/twilio-sms-service';
import { generateAIResponse } from '../../server/services/openai';
import db from '../../server/db';
import { customers, conversations, messages, vehicles, handovers } from '../../shared/lead-management-schema';
import { eq } from 'drizzle-orm';

// Mock external services
vi.mock('../../server/services/openai', () => ({
  generateAIResponse: vi.fn()
}));

vi.mock('twilio', () => ({
  Twilio: vi.fn().mockImplementation(() => ({
    messages: {
      create: vi.fn().mockResolvedValue({ sid: 'test-sid', status: 'queued' })
    }
  }))
}));

describe('Phase 1 Critical Path Integration Tests', () => {
  const testDealershipId = 1;
  let testConversationId: string;
  let testCustomerId: string;
  let testLeadId: string;

  beforeEach(async () => {
    // Set up test data
    const testCustomer = {
      dealershipId: testDealershipId,
      fullName: 'Test Customer',
      email: '<EMAIL>',
      phone: '+**********',
      deduplicationHash: 'test-hash'
    };

    const [customer] = await db.insert(customers).values(testCustomer).returning();
    testCustomerId = customer.id;

    // Create test conversation and lead setup would go here
    // For now, using mock IDs
    testConversationId = 'test-conversation-id';
    testLeadId = 'test-lead-id';
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(customers).where(eq(customers.id, testCustomerId));
    vi.clearAllMocks();
  });

  describe('Lead Ingestion → AI Response Flow', () => {
    test('should generate AI response for customer message', async () => {
      // Mock OpenAI response
      const mockAIResponse = 'Thank you for your interest! I can help you find the perfect vehicle.';
      vi.mocked(generateAIResponse).mockResolvedValue(mockAIResponse);

      // Test AI response generation
      const result = await aiResponseService.generateAndSendResponse({
        dealershipId: testDealershipId,
        conversationId: testConversationId,
        prompt: 'You are a helpful automotive sales assistant.',
        context: { customerMessage: 'I need a new car' }
      });

      expect(result.success).toBe(true);
      expect(result.content).toBe(mockAIResponse);
      expect(result.escalated).toBe(false);
    });

    test('should handle AI response with escalation trigger', async () => {
      // Create escalation trigger for human requests
      await createEscalationTrigger({
        dealershipId: testDealershipId,
        name: 'Human Request',
        description: 'Customer asks for human assistance',
        conditions: [{
          type: 'keyword',
          value: ['speak to human', 'talk to person']
        }]
      });

      // Mock messages that should trigger escalation
      const mockMessages = [
        { content: 'I want to speak to a human agent', isFromCustomer: true },
        { content: 'How can I help you?', isFromCustomer: false }
      ];

      const escalationResult = await evaluateEscalationTriggers(
        testDealershipId,
        { messages: mockMessages }
      );

      expect(escalationResult.shouldEscalate).toBe(true);
      expect(escalationResult.reason).toBe('Human Request');
    });
  });

  describe('OpenAI Error Resilience', () => {
    test('should retry on temporary failures', async () => {
      // Mock first call to fail, second to succeed
      vi.mocked(generateAIResponse)
        .mockRejectedValueOnce(new Error('rate limit exceeded'))
        .mockResolvedValueOnce('Success after retry');

      const result = await generateAIResponse(
        'Test prompt',
        'Test scenario',
        testDealershipId
      );

      expect(result).toBe('Success after retry');
      expect(generateAIResponse).toHaveBeenCalledTimes(2);
    });

    test('should return fallback response after max retries', async () => {
      // Mock all calls to fail
      vi.mocked(generateAIResponse)
        .mockRejectedValue(new Error('service unavailable'));

      const result = await generateAIResponse(
        'Test prompt',
        'Test scenario',
        testDealershipId
      );

      // Should return a fallback response
      expect(result).toContain('trouble');
      expect(result).toContain('team member');
    });

    test('should handle different error types with appropriate fallback', async () => {
      // Test rate limit error
      vi.mocked(generateAIResponse)
        .mockRejectedValueOnce(new Error('rate limit exceeded'));

      const rateLimitResult = await generateAIResponse(
        'Test prompt',
        'Test scenario',
        testDealershipId
      );

      expect(rateLimitResult).toContain('high demand');

      // Test timeout error
      vi.mocked(generateAIResponse)
        .mockRejectedValueOnce(new Error('timeout'));

      const timeoutResult = await generateAIResponse(
        'Test prompt',
        'Test scenario',
        testDealershipId
      );

      expect(timeoutResult).toContain('connection issue');
    });
  });

  describe('SMS Opt-out Handling', () => {
    test('should handle STOP keyword in SMS webhook', async () => {
      const webhookData = {
        MessageSid: 'test-sid',
        Body: 'STOP',
        From: '+**********',
        To: '+**********',
        AccountSid: 'test-account'
      };

      // Test opt-out processing
      await twilioSMSService.handleOptOut(
        testDealershipId, 
        webhookData.From, 
        'user_request'
      );

      // Verify customer is marked as opted out
      const optOutStatus = await twilioSMSService.checkOptOutStatus(
        testDealershipId,
        webhookData.From
      );

      expect(optOutStatus).toBe(true);
    });

    test('should handle START keyword for opt-in', async () => {
      // First opt out the customer
      await twilioSMSService.handleOptOut(
        testDealershipId,
        '+**********',
        'user_request'
      );

      // Then opt back in
      const webhookData = {
        MessageSid: 'test-sid',
        Body: 'START',
        From: '+**********',
        To: '+**********',
        AccountSid: 'test-account'
      };

      // This would be handled in the webhook route
      // For now, test the opt-out status directly
      const optOutStatus = await twilioSMSService.checkOptOutStatus(
        testDealershipId,
        '+**********'
      );

      expect(optOutStatus).toBe(true); // Still opted out until START is processed
    });

    test('should not send SMS to opted-out customers', async () => {
      // Opt out customer
      await twilioSMSService.handleOptOut(
        testDealershipId,
        '+**********',
        'user_request'
      );

      // Try to send SMS
      const result = await twilioSMSService.sendSMS({
        dealershipId: testDealershipId,
        toPhone: '+**********',
        message: 'Test message'
      });

      expect(result.success).toBe(false);
      expect(result.error).toContain('opted out');
    });
  });

  describe('Inventory Lifecycle Management', () => {
    test('should update lastSeen timestamp on inventory import', async () => {
      // Create test TSV content
      const tsvContent = `VIN\tMake\tModel\tYear\tPrice
TEST123456789\tToyota\tCamry\t2023\t25000
TEST987654321\tHonda\tAccord\t2022\t24000`;

      // Create temporary file
      const fs = await import('fs');
      const path = await import('path');
      const tempFile = path.join(process.cwd(), 'test-inventory.tsv');
      fs.writeFileSync(tempFile, tsvContent);

      try {
        // Process inventory
        const result = await processTsvInventory(tempFile, testDealershipId);

        expect(result.success).toBe(true);
        expect(result.stats.added).toBe(2);

        // Verify vehicles were created with proper timestamps
        const importedVehicles = await db
          .select()
          .from(vehicles)
          .where(eq(vehicles.dealershipId, testDealershipId));

        expect(importedVehicles).toHaveLength(2);
        expect(importedVehicles[0].isActive).toBe(true);
        expect(importedVehicles[0].lastSeen).toBeDefined();

      } finally {
        // Clean up
        if (fs.existsSync(tempFile)) {
          fs.unlinkSync(tempFile);
        }
        await db.delete(vehicles).where(eq(vehicles.dealershipId, testDealershipId));
      }
    });

    test('should mark stale vehicles as inactive', async () => {
      // Create test vehicle with old lastSeen date
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 35); // 35 days ago

      const testVehicle = {
        dealershipId: testDealershipId,
        vin: 'OLDVEHICLE123',
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        isActive: true,
        lastSeen: oldDate
      };

      const [vehicle] = await db.insert(vehicles).values(testVehicle).returning();

      try {
        // Run cleanup
        const deactivatedCount = await cleanupStaleInventory(testDealershipId);

        expect(deactivatedCount).toBe(1);

        // Verify vehicle is now inactive
        const updatedVehicle = await db
          .select()
          .from(vehicles)
          .where(eq(vehicles.id, vehicle.id))
          .limit(1);

        expect(updatedVehicle[0].isActive).toBe(false);

      } finally {
        await db.delete(vehicles).where(eq(vehicles.id, vehicle.id));
      }
    });
  });

  describe('End-to-End Critical Path', () => {
    test('should handle complete customer interaction flow', async () => {
      // Mock AI response
      vi.mocked(generateAIResponse).mockResolvedValue(
        'Thank you for your interest! How can I help you today?'
      );

      // 1. Customer sends message (simulate inbound message)
      const customerMessage = 'I need help finding a car';

      // 2. AI generates response
      const aiResult = await aiResponseService.generateAndSendResponse({
        dealershipId: testDealershipId,
        conversationId: testConversationId,
        prompt: 'You are a helpful automotive sales assistant.',
        context: { customerMessage }
      });

      expect(aiResult.success).toBe(true);
      expect(aiResult.escalated).toBe(false);

      // 3. Customer asks for human (should trigger escalation)
      const escalationMessage = 'I want to speak to a human agent';

      // Create escalation trigger
      await createEscalationTrigger({
        dealershipId: testDealershipId,
        name: 'Human Request Test',
        description: 'Test escalation trigger',
        conditions: [{
          type: 'keyword',
          value: ['speak to human', 'human agent']
        }]
      });

      const escalationResult = await evaluateEscalationTriggers(
        testDealershipId,
        { messages: [{ content: escalationMessage, isFromCustomer: true }] }
      );

      expect(escalationResult.shouldEscalate).toBe(true);

      // 4. Verify the complete flow works without errors
      expect(true).toBe(true); // All previous assertions passed
    });
  });
});