/**
 * Infrastructure Test - Verify testing setup works
 */

import { describe, test, expect, vi } from 'vitest';

describe('Testing Infrastructure Verification', () => {
  test('should have basic test functionality', () => {
    expect(1 + 1).toBe(2);
  });

  test('should support mocking', () => {
    const mockFn = vi.fn().mockReturnValue('mocked');
    expect(mockFn()).toBe('mocked');
  });

  test('should have environment variables', () => {
    expect(process.env.NODE_ENV).toBe('test');
  });

  test('should support async operations', async () => {
    const promise = Promise.resolve('async test');
    await expect(promise).resolves.toBe('async test');
  });
});