/**
 * Escalation Flow Integration Tests
 * 
 * Tests the complete escalation flow from trigger detection to handover creation
 */

import { describe, test, expect, beforeEach, afterEach, vi } from 'vitest';
import { ConversationService } from '../../server/services/conversation-service';
import { HandoverService } from '../../server/services/handover-service';
import { aiResponseService } from '../../server/services/ai-response-service';
import { 
  evaluateEscalationTriggers, 
  createEscalationTrigger,
  getEscalationTriggers,
  deleteEscalationTrigger 
} from '../../server/services/escalation-triggers';
import { 
  installDefaultEscalationTriggers,
  ensureEscalationTriggers 
} from '../../server/services/default-escalation-triggers';
import { generateAIResponse } from '../../server/services/openai';
import db from '../../server/db';
import { 
  customers, 
  conversations, 
  messages, 
  leads, 
  handovers 
} from '../../shared/lead-management-schema';
import { escalationTriggers } from '../../shared/schema-extensions';
import { eq, and } from 'drizzle-orm';

// Mock OpenAI
vi.mock('../../server/services/openai', () => ({
  generateAIResponse: vi.fn()
}));

describe('Escalation Flow Tests', () => {
  const testDealershipId = 1;
  let testConversationId: string;
  let testCustomerId: string;
  let testLeadId: string;
  let conversationService: ConversationService;
  let handoverService: HandoverService;

  beforeEach(async () => {
    conversationService = new ConversationService();
    handoverService = new HandoverService();

    // Clean up any existing escalation triggers for test dealership
    await db.delete(escalationTriggers).where(eq(escalationTriggers.dealershipId, testDealershipId));

    // Create test customer
    const testCustomer = {
      dealershipId: testDealershipId,
      fullName: 'Test Customer',
      email: '<EMAIL>',
      phone: '+1234567890',
      deduplicationHash: 'escalation-test-hash'
    };

    const [customer] = await db.insert(customers).values(testCustomer).returning();
    testCustomerId = customer.id;

    // Create test lead
    const testLead = {
      dealershipId: testDealershipId,
      customerId: testCustomerId,
      leadNumber: 'LEAD-ESC-001',
      source: 'website_form' as any,
      deduplicationHash: 'lead-escalation-hash'
    };

    const [lead] = await db.insert(leads).values(testLead).returning();
    testLeadId = lead.id;

    // Create test conversation
    const testConversation = {
      dealershipId: testDealershipId,
      leadId: testLeadId,
      customerId: testCustomerId,
      subject: 'Escalation Test Conversation',
      status: 'active' as any,
      channel: 'chat'
    };

    const [conversation] = await db.insert(conversations).values(testConversation).returning();
    testConversationId = conversation.id;
  });

  afterEach(async () => {
    // Clean up test data
    await db.delete(handovers).where(eq(handovers.conversationId, testConversationId));
    await db.delete(messages).where(eq(messages.conversationId, testConversationId));
    await db.delete(conversations).where(eq(conversations.id, testConversationId));
    await db.delete(leads).where(eq(leads.id, testLeadId));
    await db.delete(customers).where(eq(customers.id, testCustomerId));
    await db.delete(escalationTriggers).where(eq(escalationTriggers.dealershipId, testDealershipId));
    
    vi.clearAllMocks();
  });

  describe('Escalation Trigger Management', () => {
    test('should install default escalation triggers', async () => {
      await installDefaultEscalationTriggers(testDealershipId);

      const triggers = await getEscalationTriggers(testDealershipId);
      
      expect(triggers.length).toBeGreaterThan(0);
      
      // Check for key triggers
      const triggerNames = triggers.map(t => t.name);
      expect(triggerNames).toContain('Customer Requests Human');
      expect(triggerNames).toContain('Legal or Compliance Inquiry');
      expect(triggerNames).toContain('Negative Sentiment');
    });

    test('should not reinstall triggers if they already exist', async () => {
      // Install triggers first time
      await installDefaultEscalationTriggers(testDealershipId);
      const firstInstallCount = (await getEscalationTriggers(testDealershipId)).length;

      // Try to ensure triggers again
      await ensureEscalationTriggers(testDealershipId);
      const secondInstallCount = (await getEscalationTriggers(testDealershipId)).length;

      expect(secondInstallCount).toBe(firstInstallCount);
    });

    test('should create custom escalation trigger', async () => {
      const triggerData = {
        dealershipId: testDealershipId,
        name: 'Custom Pricing Trigger',
        description: 'Trigger when customer mentions pricing',
        conditions: [{
          type: 'keyword' as any,
          value: ['price', 'cost', 'expensive', 'cheap']
        }]
      };

      const trigger = await createEscalationTrigger(triggerData);

      expect(trigger.name).toBe('Custom Pricing Trigger');
      expect(trigger.dealershipId).toBe(testDealershipId);
      expect(trigger.isActive).toBe(true);
    });
  });

  describe('Trigger Evaluation', () => {
    beforeEach(async () => {
      // Create test triggers
      await createEscalationTrigger({
        dealershipId: testDealershipId,
        name: 'Human Request',
        description: 'Customer asks for human',
        conditions: [{
          type: 'keyword',
          value: ['human', 'person', 'agent', 'representative']
        }]
      });

      await createEscalationTrigger({
        dealershipId: testDealershipId,
        name: 'Negative Sentiment',
        description: 'Very negative customer sentiment',
        conditions: [{
          type: 'sentiment',
          threshold: 0.2
        }]
      });

      await createEscalationTrigger({
        dealershipId: testDealershipId,
        name: 'Legal Keywords',
        description: 'Legal or compliance issues',
        conditions: [{
          type: 'keyword',
          value: ['lawsuit', 'lawyer', 'legal', 'sue', 'attorney']
        }]
      });
    });

    test('should trigger escalation for human request keywords', async () => {
      const messages = [
        { content: 'I want to speak to a human agent', isFromCustomer: true },
        { content: 'How can I help you?', isFromCustomer: false }
      ];

      const result = await evaluateEscalationTriggers(testDealershipId, { messages });

      expect(result.shouldEscalate).toBe(true);
      expect(result.reason).toBe('Human Request');
    });

    test('should trigger escalation for legal keywords', async () => {
      const messages = [
        { content: 'I will contact my lawyer about this', isFromCustomer: true },
        { content: 'How can I help you?', isFromCustomer: false }
      ];

      const result = await evaluateEscalationTriggers(testDealershipId, { messages });

      expect(result.shouldEscalate).toBe(true);
      expect(result.reason).toBe('Legal Keywords');
    });

    test('should not trigger escalation for normal conversation', async () => {
      const messages = [
        { content: 'I am looking for a new car', isFromCustomer: true },
        { content: 'Great! I can help you find the perfect vehicle.', isFromCustomer: false }
      ];

      const result = await evaluateEscalationTriggers(testDealershipId, { messages });

      expect(result.shouldEscalate).toBe(false);
    });

    test('should handle multiple matching triggers', async () => {
      const messages = [
        { content: 'I want to talk to a human about legal action', isFromCustomer: true }
      ];

      const result = await evaluateEscalationTriggers(testDealershipId, { messages });

      expect(result.shouldEscalate).toBe(true);
      // Should match the first trigger found
      expect(['Human Request', 'Legal Keywords']).toContain(result.reason);
    });

    test('should evaluate repeated questions trigger', async () => {
      await createEscalationTrigger({
        dealershipId: testDealershipId,
        name: 'Repeated Questions',
        description: 'Customer asks similar questions multiple times',
        conditions: [{
          type: 'repeated_questions',
          threshold: 2
        }]
      });

      const messages = [
        { content: 'What is the price of this car?', isFromCustomer: true },
        { content: 'The price is $25,000', isFromCustomer: false },
        { content: 'How much does this car cost?', isFromCustomer: true },
        { content: 'As I mentioned, it costs $25,000', isFromCustomer: false },
        { content: 'What is the cost of this vehicle?', isFromCustomer: true }
      ];

      const result = await evaluateEscalationTriggers(testDealershipId, { messages });

      expect(result.shouldEscalate).toBe(true);
      expect(result.reason).toBe('Repeated Questions');
    });
  });

  describe('Handover Creation Flow', () => {
    beforeEach(async () => {
      // Create human request trigger
      await createEscalationTrigger({
        dealershipId: testDealershipId,
        name: 'Human Request',
        description: 'Customer asks for human',
        conditions: [{
          type: 'keyword',
          value: ['human agent', 'speak to person']
        }]
      });
    });

    test('should create handover when escalation is triggered', async () => {
      const handoverRequest = {
        conversationId: testConversationId,
        reason: 'customer_request' as any,
        description: 'Customer requested human assistance',
        urgency: 'medium' as any
      };

      const result = await handoverService.createHandover(testDealershipId, handoverRequest);

      expect(result.success).toBe(true);
      expect(result.handoverId).toBeDefined();
      expect(result.status).toBe('pending');

      // Verify handover was created in database
      const handover = await handoverService.getHandoverById(testDealershipId, result.handoverId!);
      expect(handover).toBeTruthy();
      expect(handover!.reason).toBe('customer_request');
    });

    test('should update conversation status to escalated on handover', async () => {
      const handoverRequest = {
        conversationId: testConversationId,
        reason: 'customer_request' as any,
        description: 'Customer requested human assistance'
      };

      await handoverService.createHandover(testDealershipId, handoverRequest);

      // Check conversation status
      const conversation = await conversationService.getConversation(
        testDealershipId, 
        testConversationId,
        { includeMessages: false }
      );

      expect(conversation?.conversation.status).toBe('escalated');
    });

    test('should not create duplicate handovers for same conversation', async () => {
      const handoverRequest = {
        conversationId: testConversationId,
        reason: 'customer_request' as any,
        description: 'First handover request'
      };

      // Create first handover
      const firstResult = await handoverService.createHandover(testDealershipId, handoverRequest);
      expect(firstResult.success).toBe(true);

      // Try to create second handover
      const secondResult = await handoverService.createHandover(testDealershipId, handoverRequest);
      expect(secondResult.success).toBe(false);
      expect(secondResult.errors).toContain('There is already a pending handover for this conversation');
    });
  });

  describe('AI Response Service Integration', () => {
    beforeEach(async () => {
      // Install default triggers
      await installDefaultEscalationTriggers(testDealershipId);
      
      // Mock OpenAI response
      vi.mocked(generateAIResponse).mockResolvedValue(
        'Thank you for your message. How can I help you today?'
      );
    });

    test('should escalate before generating AI response when trigger detected', async () => {
      // Add messages to conversation that should trigger escalation
      await conversationService.sendReply(testDealershipId, {
        conversationId: testConversationId,
        content: 'I want to speak to a human agent right now',
        sender: 'customer'
      });

      const result = await aiResponseService.generateAndSendResponse({
        dealershipId: testDealershipId,
        conversationId: testConversationId,
        prompt: 'You are a helpful automotive sales assistant.'
      });

      expect(result.escalated).toBe(true);
      expect(result.escalationReason).toContain('Customer Requests Human');
      expect(result.handoverId).toBeDefined();
    });

    test('should generate AI response when no escalation triggers', async () => {
      // Add normal customer message
      await conversationService.sendReply(testDealershipId, {
        conversationId: testConversationId,
        content: 'I am looking for a new Toyota Camry',
        sender: 'customer'
      });

      const result = await aiResponseService.generateAndSendResponse({
        dealershipId: testDealershipId,
        conversationId: testConversationId,
        prompt: 'You are a helpful automotive sales assistant.'
      });

      expect(result.success).toBe(true);
      expect(result.escalated).toBe(false);
      expect(result.content).toBeDefined();
      expect(result.messageId).toBeDefined();
    });

    test('should handle post-response escalation', async () => {
      // Mock AI response that might trigger escalation after generation
      vi.mocked(generateAIResponse).mockResolvedValue(
        'I understand you are frustrated. Let me help you with that.'
      );

      // Add customer message that might trigger escalation after AI response
      await conversationService.sendReply(testDealershipId, {
        conversationId: testConversationId,
        content: 'This is terrible service, I am very angry',
        sender: 'customer'
      });

      const result = await aiResponseService.generateAndSendResponse({
        dealershipId: testDealershipId,
        conversationId: testConversationId,
        prompt: 'You are a helpful automotive sales assistant.'
      });

      // The response should be generated, but escalation might be triggered
      expect(result.success).toBe(true);
      expect(result.content).toBeDefined();
    });
  });

  describe('Escalation Analytics', () => {
    test('should track escalation metrics', async () => {
      await installDefaultEscalationTriggers(testDealershipId);

      // Create multiple test scenarios
      const scenarios = [
        { message: 'I want to speak to a human', expectedTrigger: 'Customer Requests Human' },
        { message: 'I will call my lawyer', expectedTrigger: 'Legal or Compliance Inquiry' },
        { message: 'This is terrible service', expectedTrigger: 'Angry or Upset Customer' }
      ];

      const results = [];
      
      for (const scenario of scenarios) {
        const result = await evaluateEscalationTriggers(testDealershipId, {
          messages: [{ content: scenario.message, isFromCustomer: true }]
        });
        
        results.push({
          scenario: scenario.message,
          escalated: result.shouldEscalate,
          reason: result.reason
        });
      }

      // Verify all scenarios triggered escalation
      const escalatedCount = results.filter(r => r.escalated).length;
      expect(escalatedCount).toBe(3);

      // Verify specific triggers were matched
      expect(results.find(r => r.reason === 'Customer Requests Human')).toBeTruthy();
      expect(results.find(r => r.reason === 'Legal or Compliance Inquiry')).toBeTruthy();
      expect(results.find(r => r.reason === 'Angry or Upset Customer')).toBeTruthy();
    });
  });
});