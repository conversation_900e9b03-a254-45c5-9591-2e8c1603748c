#!/usr/bin/env node

/**
 * Load testing script for Rylie AI Platform
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5001';
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

async function makeRequest(url, options = {}) {
  const startTime = Date.now();
  try {
    const response = await fetch(url, {
      timeout: 10000,
      ...options
    });
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: response.ok,
      status: response.status,
      responseTime,
      error: null
    };
  } catch (error) {
    const endTime = Date.now();
    const responseTime = endTime - startTime;
    
    return {
      success: false,
      status: 0,
      responseTime,
      error: error.message
    };
  }
}

async function runLoadTest(testName, url, options, concurrency, totalRequests) {
  log(`\n🚀 Running ${testName}`, 'yellow');
  log(`   Concurrency: ${concurrency} | Total Requests: ${totalRequests}`, 'blue');
  
  const results = [];
  const startTime = Date.now();
  
  // Create batches of concurrent requests
  const batchSize = concurrency;
  const batches = Math.ceil(totalRequests / batchSize);
  
  for (let batch = 0; batch < batches; batch++) {
    const batchStart = Date.now();
    const requestsInBatch = Math.min(batchSize, totalRequests - (batch * batchSize));
    
    // Create concurrent requests for this batch
    const promises = [];
    for (let i = 0; i < requestsInBatch; i++) {
      promises.push(makeRequest(url, options));
    }
    
    // Wait for all requests in this batch to complete
    const batchResults = await Promise.all(promises);
    results.push(...batchResults);
    
    const batchTime = Date.now() - batchStart;
    process.stdout.write(`\r   Progress: ${results.length}/${totalRequests} requests (${Math.round((results.length/totalRequests)*100)}%) - Batch ${batch + 1}/${batches} completed in ${batchTime}ms`);
  }
  
  const totalTime = Date.now() - startTime;
  
  // Calculate statistics
  const successful = results.filter(r => r.success).length;
  const failed = results.filter(r => !r.success).length;
  const responseTimes = results.map(r => r.responseTime);
  const avgResponseTime = responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length;
  const minResponseTime = Math.min(...responseTimes);
  const maxResponseTime = Math.max(...responseTimes);
  const requestsPerSecond = (totalRequests / totalTime) * 1000;
  
  // Calculate percentiles
  const sortedTimes = responseTimes.sort((a, b) => a - b);
  const p50 = sortedTimes[Math.floor(sortedTimes.length * 0.5)];
  const p95 = sortedTimes[Math.floor(sortedTimes.length * 0.95)];
  const p99 = sortedTimes[Math.floor(sortedTimes.length * 0.99)];
  
  console.log('\n');
  log(`📊 ${testName} Results:`, 'bold');
  log(`   Total Requests: ${totalRequests}`, 'blue');
  log(`   Successful: ${successful} (${Math.round((successful/totalRequests)*100)}%)`, successful === totalRequests ? 'green' : 'yellow');
  log(`   Failed: ${failed} (${Math.round((failed/totalRequests)*100)}%)`, failed === 0 ? 'green' : 'red');
  log(`   Total Time: ${totalTime}ms`, 'blue');
  log(`   Requests/sec: ${requestsPerSecond.toFixed(2)}`, 'blue');
  log(`   Avg Response Time: ${avgResponseTime.toFixed(2)}ms`, 'blue');
  log(`   Min Response Time: ${minResponseTime}ms`, 'blue');
  log(`   Max Response Time: ${maxResponseTime}ms`, 'blue');
  log(`   50th Percentile: ${p50}ms`, 'blue');
  log(`   95th Percentile: ${p95}ms`, 'blue');
  log(`   99th Percentile: ${p99}ms`, 'blue');
  
  return {
    testName,
    totalRequests,
    successful,
    failed,
    totalTime,
    requestsPerSecond,
    avgResponseTime,
    minResponseTime,
    maxResponseTime,
    p50,
    p95,
    p99
  };
}

async function runAllLoadTests() {
  log('🧪 Starting Rylie AI Platform Load Tests', 'bold');
  log('=' .repeat(60), 'blue');
  
  const testResults = [];
  
  // Test 1: Health Check Load Test
  const healthResult = await runLoadTest(
    'Health Check Load Test',
    `${BASE_URL}/health`,
    { method: 'GET' },
    10, // 10 concurrent requests
    100 // 100 total requests
  );
  testResults.push(healthResult);
  
  // Test 2: Conversation API Load Test
  const conversationResult = await runLoadTest(
    'Conversation API Load Test',
    `${BASE_URL}/api/test-conversation`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        message: 'I need a reliable car for my family',
        customerName: 'Load Test Customer'
      })
    },
    5, // 5 concurrent requests (lower for POST)
    50 // 50 total requests
  );
  testResults.push(conversationResult);
  
  // Test 3: Mixed Load Test
  const mixedResult = await runLoadTest(
    'Mixed Endpoint Load Test',
    `${BASE_URL}/health`, // Using health for simplicity
    { method: 'GET' },
    20, // 20 concurrent requests
    200 // 200 total requests
  );
  testResults.push(mixedResult);
  
  // Summary
  log('\n' + '=' .repeat(60), 'blue');
  log('📋 Load Test Summary', 'bold');
  
  let totalRequests = 0;
  let totalSuccessful = 0;
  let totalFailed = 0;
  let avgRequestsPerSecond = 0;
  
  testResults.forEach(result => {
    totalRequests += result.totalRequests;
    totalSuccessful += result.successful;
    totalFailed += result.failed;
    avgRequestsPerSecond += result.requestsPerSecond;
  });
  
  avgRequestsPerSecond = avgRequestsPerSecond / testResults.length;
  
  log(`\nOverall Statistics:`, 'yellow');
  log(`   Total Requests: ${totalRequests}`, 'blue');
  log(`   Total Successful: ${totalSuccessful} (${Math.round((totalSuccessful/totalRequests)*100)}%)`, 'green');
  log(`   Total Failed: ${totalFailed} (${Math.round((totalFailed/totalRequests)*100)}%)`, totalFailed === 0 ? 'green' : 'red');
  log(`   Average RPS: ${avgRequestsPerSecond.toFixed(2)}`, 'blue');
  
  // Performance Assessment
  log(`\n🎯 Performance Assessment:`, 'bold');
  
  if (totalFailed === 0) {
    log(`   ✅ Reliability: EXCELLENT (0% failure rate)`, 'green');
  } else if (totalFailed / totalRequests < 0.01) {
    log(`   ✅ Reliability: GOOD (< 1% failure rate)`, 'yellow');
  } else {
    log(`   ❌ Reliability: POOR (> 1% failure rate)`, 'red');
  }
  
  if (avgRequestsPerSecond > 100) {
    log(`   ✅ Throughput: EXCELLENT (> 100 RPS)`, 'green');
  } else if (avgRequestsPerSecond > 50) {
    log(`   ✅ Throughput: GOOD (> 50 RPS)`, 'yellow');
  } else {
    log(`   ⚠️  Throughput: MODERATE (< 50 RPS)`, 'yellow');
  }
  
  const avgP95 = testResults.reduce((sum, r) => sum + r.p95, 0) / testResults.length;
  if (avgP95 < 100) {
    log(`   ✅ Latency: EXCELLENT (95th percentile < 100ms)`, 'green');
  } else if (avgP95 < 500) {
    log(`   ✅ Latency: GOOD (95th percentile < 500ms)`, 'yellow');
  } else {
    log(`   ⚠️  Latency: SLOW (95th percentile > 500ms)`, 'yellow');
  }
  
  log(`\n🏁 Load testing completed successfully!`, 'green');
  log(`📊 The platform handled ${totalRequests} requests with ${Math.round((totalSuccessful/totalRequests)*100)}% success rate`, 'blue');
}

// Check if server is running first
async function checkServerStatus() {
  try {
    const response = await fetch(`${BASE_URL}/health`, { timeout: 5000 });
    if (response.ok) {
      log('✅ Test server is running and ready for load testing', 'green');
      return true;
    } else {
      log('❌ Test server responded with error status', 'red');
      return false;
    }
  } catch (error) {
    log('❌ Cannot connect to test server. Please start the server first:', 'red');
    log('   node platform-test.js', 'yellow');
    return false;
  }
}

// Main execution
async function main() {
  const serverReady = await checkServerStatus();
  if (serverReady) {
    await runAllLoadTests();
  } else {
    process.exit(1);
  }
}

main().catch(error => {
  log(`\n❌ Load test failed: ${error.message}`, 'red');
  process.exit(1);
});
