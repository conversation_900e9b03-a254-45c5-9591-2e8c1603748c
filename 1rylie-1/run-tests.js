#!/usr/bin/env node

/**
 * Comprehensive test suite for Rylie AI Platform
 */

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:5001';
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(message, color = 'reset') {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

function logTest(testName, status, details = '') {
  const statusColor = status === 'PASS' ? 'green' : status === 'FAIL' ? 'red' : 'yellow';
  const statusSymbol = status === 'PASS' ? '✅' : status === 'FAIL' ? '❌' : '⚠️';
  log(`${statusSymbol} ${testName}: ${status}`, statusColor);
  if (details) {
    log(`   ${details}`, 'blue');
  }
}

async function testEndpoint(name, url, options = {}) {
  try {
    const response = await fetch(url, {
      timeout: 5000,
      ...options
    });
    
    const data = await response.json();
    
    if (response.ok) {
      logTest(name, 'PASS', `Status: ${response.status}`);
      return { success: true, data, status: response.status };
    } else {
      logTest(name, 'FAIL', `Status: ${response.status} - ${data.message || 'Unknown error'}`);
      return { success: false, data, status: response.status };
    }
  } catch (error) {
    logTest(name, 'FAIL', `Error: ${error.message}`);
    return { success: false, error: error.message };
  }
}

async function runTests() {
  log('\n🧪 Starting Rylie AI Platform Tests', 'bold');
  log('=' .repeat(50), 'blue');
  
  let passedTests = 0;
  let totalTests = 0;
  
  // Test 1: Health Check
  totalTests++;
  log('\n📊 Testing Health Check Endpoint', 'yellow');
  const healthResult = await testEndpoint(
    'Health Check',
    `${BASE_URL}/health`
  );
  if (healthResult.success) {
    passedTests++;
    log(`   Platform: ${healthResult.data.platform}`, 'blue');
    log(`   Version: ${healthResult.data.version}`, 'blue');
    log(`   Timestamp: ${healthResult.data.timestamp}`, 'blue');
  }
  
  // Test 2: Conversation API
  totalTests++;
  log('\n💬 Testing Conversation API', 'yellow');
  const conversationResult = await testEndpoint(
    'Conversation API',
    `${BASE_URL}/api/test-conversation`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: 'I am looking for a reliable family car under $30,000',
        customerName: 'Test Customer'
      })
    }
  );
  if (conversationResult.success) {
    passedTests++;
    const conv = conversationResult.data.conversation;
    log(`   Conversation ID: ${conv.id}`, 'blue');
    log(`   Customer: ${conv.customer}`, 'blue');
    log(`   Messages: ${conv.messages.length}`, 'blue');
    log(`   AI Response: "${conv.messages[1].content.substring(0, 50)}..."`, 'blue');
  }
  
  // Test 3: Error Handling
  totalTests++;
  log('\n🚫 Testing Error Handling', 'yellow');
  const errorResult = await testEndpoint(
    'Error Handling (404)',
    `${BASE_URL}/api/nonexistent-endpoint`
  );
  if (!errorResult.success && errorResult.status === 404) {
    passedTests++;
    logTest('Error Handling (404)', 'PASS', 'Correctly returns 404 for non-existent endpoints');
  } else {
    logTest('Error Handling (404)', 'FAIL', 'Should return 404 for non-existent endpoints');
  }
  
  // Test 4: CORS Headers
  totalTests++;
  log('\n🌐 Testing CORS Configuration', 'yellow');
  try {
    const corsResponse = await fetch(`${BASE_URL}/health`, {
      method: 'OPTIONS'
    });
    
    if (corsResponse.ok) {
      passedTests++;
      logTest('CORS Configuration', 'PASS', 'OPTIONS request successful');
    } else {
      logTest('CORS Configuration', 'FAIL', `OPTIONS request failed: ${corsResponse.status}`);
    }
  } catch (error) {
    logTest('CORS Configuration', 'FAIL', `Error: ${error.message}`);
  }
  
  // Test 5: JSON Parsing
  totalTests++;
  log('\n📝 Testing JSON Request Parsing', 'yellow');
  const jsonResult = await testEndpoint(
    'JSON Request Parsing',
    `${BASE_URL}/api/test-conversation`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        message: 'Test message with special characters: àáâãäåæçèéêë',
        customerName: 'José María'
      })
    }
  );
  if (jsonResult.success) {
    passedTests++;
    log(`   Successfully parsed special characters`, 'blue');
  }
  
  // Test 6: Performance Test
  totalTests++;
  log('\n⚡ Testing Response Performance', 'yellow');
  const startTime = Date.now();
  const perfResult = await testEndpoint(
    'Performance Test',
    `${BASE_URL}/health`
  );
  const responseTime = Date.now() - startTime;
  
  if (perfResult.success) {
    if (responseTime < 1000) {
      passedTests++;
      logTest('Performance Test', 'PASS', `Response time: ${responseTime}ms`);
    } else {
      logTest('Performance Test', 'WARN', `Response time: ${responseTime}ms (slow)`);
    }
  }
  
  // Summary
  log('\n' + '=' .repeat(50), 'blue');
  log('📋 Test Summary', 'bold');
  log(`Total Tests: ${totalTests}`, 'blue');
  log(`Passed: ${passedTests}`, 'green');
  log(`Failed: ${totalTests - passedTests}`, 'red');
  log(`Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`, 'yellow');
  
  if (passedTests === totalTests) {
    log('\n🎉 All tests passed! Platform is working correctly.', 'green');
  } else {
    log('\n⚠️  Some tests failed. Please check the issues above.', 'yellow');
  }
  
  log('\n🔧 Next Steps:', 'bold');
  log('1. Fix any TypeScript compilation errors', 'blue');
  log('2. Set up proper database connection', 'blue');
  log('3. Configure OpenAI API key for real AI responses', 'blue');
  log('4. Set up SendGrid for email functionality', 'blue');
  log('5. Run integration tests with real data', 'blue');
}

// Run the tests
runTests().catch(error => {
  log(`\n❌ Test suite failed: ${error.message}`, 'red');
  process.exit(1);
});
