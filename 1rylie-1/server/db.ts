import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';
import logger from './utils/logger';

// Import from main schema file temporarily to avoid schema-resolver issues
import * as schema from '../shared/schema';

// Database connection configuration
const connectionString = process.env.DATABASE_URL || 'postgres://postgres:postgres@localhost:5432/rylie';

// Create postgres client
const client = postgres(connectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
  prepare: false,
});

logger.info(`Attempting to connect to database with URL: ${connectionString}`);

// Create drizzle instance with all schema tables
const db = drizzle(client, { schema });

/**
 * Enhanced execute function with automatic retry logic
 * Provides resilient database operations with exponential backoff
 */
export async function executeQuery<T>(
  queryFn: () => Promise<T>,
  retries: number = 3
): Promise<T> {
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      return await queryFn();
    } catch (error: any) {
      logger.warn(`Query attempt ${attempt} failed:`, error.message);
      
      // Check if it's a connection error
      if (error.code === 'ECONNRESET' || error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        if (attempt < retries) {
          logger.info(`Attempting query retry ${attempt + 1}/${retries}...`);
          // Wait before retrying (exponential backoff)
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
          continue;
        }
      }
      
      // Re-throw error if max retries reached or non-connection error
      throw error;
    }
  }
  
  throw new Error('Query failed after all retry attempts');
}

/**
 * Check database connection health
 */
export async function checkDatabaseConnection(): Promise<boolean> {
  try {
    await executeQuery(async () => {
      const result = await client`SELECT 1 as test`;
      return result[0]?.test === 1;
    });
    return true;
  } catch (error) {
    logger.error('Database health check failed:', error);
    return false;
  }
}

export default db;
export { db };