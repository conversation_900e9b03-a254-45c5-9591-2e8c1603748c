import { eq, and, desc } from 'drizzle-orm';
import db from '../db';
import {
  conversations,
  messages,
  leads,
  leadActivities,
  type InsertMessage,
  type Message,
  type Conversation,
  type MessageSender,
  type MessageType
} from '../../shared/lead-management-schema';
import logger from '../utils/logger';

export interface ReplyMessageData {
  conversationId: string;
  content: string;
  contentType?: 'text' | 'html' | 'markdown';
  sender: MessageSender;
  senderUserId?: number;
  senderName?: string;
  subject?: string;
  attachments?: Array<{
    filename: string;
    contentType: string;
    size: number;
    url: string;
  }>;
}

export interface ReplyResult {
  success: boolean;
  messageId?: string;
  conversationId?: string;
  timestamp?: Date;
  errors: string[];
  conversationNotFound?: boolean;
}

export interface ConversationDetails {
  conversation: Conversation;
  messages: Message[];
  totalMessages: number;
}

export class ConversationService {
  /**
   * Send a reply message to a conversation
   */
  async sendReply(
    dealershipId: number,
    replyData: ReplyMessageData
  ): Promise<ReplyResult> {
    const errors: string[] = [];

    try {
      logger.info('Sending reply message', {
        dealershipId,
        conversationId: replyData.conversationId,
        sender: replyData.sender
      });

      // Verify conversation exists and belongs to dealership
      const conversationResults = await db
        .select()
        .from(conversations)
        .where(and(
          eq(conversations.id, replyData.conversationId),
          eq(conversations.dealershipId, dealershipId)
        ))
        .limit(1);

      if (conversationResults.length === 0) {
        return {
          success: false,
          errors: ['Conversation not found'],
          conversationNotFound: true
        };
      }

      const conversation = conversationResults[0];

      // Determine message type based on sender
      let messageType: MessageType;
      switch (replyData.sender) {
        case 'customer':
          messageType = 'inbound';
          break;
        case 'ai':
        case 'agent':
          messageType = 'outbound';
          break;
        case 'system':
          messageType = 'system';
          break;
        default:
          messageType = 'outbound';
      }

      // Create the message
      const messageData: InsertMessage = {
        conversationId: replyData.conversationId,
        content: replyData.content,
        contentType: replyData.contentType || 'text',
        subject: replyData.subject,
        type: messageType,
        sender: replyData.sender,
        senderUserId: replyData.senderUserId,
        senderName: replyData.senderName,
        attachments: replyData.attachments || [],
        isRead: replyData.sender !== 'customer' // Mark as read if not from customer
      };

      const [newMessage] = await db
        .insert(messages)
        .values(messageData)
        .returning();

      // Update conversation metadata
      await db
        .update(conversations)
        .set({
          lastMessageAt: new Date(),
          messageCount: conversation.messageCount + 1,
          updatedAt: new Date(),
          // Update status based on sender
          status: replyData.sender === 'customer' ? 'waiting_response' : 'active'
        })
        .where(eq(conversations.id, replyData.conversationId));

      // Log activity on the lead
      await db.insert(leadActivities).values({
        leadId: conversation.leadId,
        userId: replyData.senderUserId,
        type: 'message_sent',
        description: `${replyData.sender} sent a message: ${replyData.content.substring(0, 100)}${replyData.content.length > 100 ? '...' : ''}`,
        messageId: newMessage.id
      });

      logger.info('Reply message sent successfully', {
        messageId: newMessage.id,
        conversationId: replyData.conversationId,
        sender: replyData.sender
      });

      return {
        success: true,
        messageId: newMessage.id,
        conversationId: replyData.conversationId,
        timestamp: newMessage.createdAt,
        errors
      };

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Reply message failed', {
        error: err.message,
        dealershipId,
        conversationId: replyData.conversationId
      });

      errors.push(`Failed to send reply: ${err.message}`);

      return {
        success: false,
        errors
      };
    }
  }

  /**
   * Get conversation details with messages
   */
  async getConversation(
    dealershipId: number,
    conversationId: string,
    options: {
      includeMessages?: boolean;
      messageLimit?: number;
      messageOffset?: number;
    } = {}
  ): Promise<ConversationDetails | null> {
    try {
      const { includeMessages = true, messageLimit = 50, messageOffset = 0 } = options;

      // Get conversation
      const conversationResults = await db
        .select()
        .from(conversations)
        .where(and(
          eq(conversations.id, conversationId),
          eq(conversations.dealershipId, dealershipId)
        ))
        .limit(1);

      if (conversationResults.length === 0) {
        return null;
      }

      const conversation = conversationResults[0];
      let conversationMessages: Message[] = [];
      let totalMessages = 0;

      if (includeMessages) {
        // Get messages for the conversation
        conversationMessages = await db
          .select()
          .from(messages)
          .where(eq(messages.conversationId, conversationId))
          .orderBy(desc(messages.createdAt))
          .limit(messageLimit)
          .offset(messageOffset);

        // Get total message count
        const messageCountResults = await db
          .select({ count: messages.id })
          .from(messages)
          .where(eq(messages.conversationId, conversationId));

        totalMessages = messageCountResults.length;
      }

      return {
        conversation,
        messages: conversationMessages,
        totalMessages
      };

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Get conversation failed', {
        error: err.message,
        dealershipId,
        conversationId
      });

      return null;
    }
  }

  /**
   * Get conversations for a dealership
   */
  async getConversations(
    dealershipId: number,
    options: {
      limit?: number;
      offset?: number;
      status?: string;
      leadId?: string;
      customerId?: string;
    } = {}
  ): Promise<Conversation[]> {
    try {
      const { limit = 50, offset = 0, status, leadId, customerId } = options;

      let query = db
        .select()
        .from(conversations)
        .where(eq(conversations.dealershipId, dealershipId));

      if (status) {
        query = query.where(and(
          eq(conversations.dealershipId, dealershipId),
          eq(conversations.status, status as any)
        ));
      }

      if (leadId) {
        query = query.where(and(
          eq(conversations.dealershipId, dealershipId),
          eq(conversations.leadId, leadId)
        ));
      }

      if (customerId) {
        query = query.where(and(
          eq(conversations.dealershipId, dealershipId),
          eq(conversations.customerId, customerId)
        ));
      }

      return query
        .orderBy(desc(conversations.lastMessageAt))
        .limit(limit)
        .offset(offset);

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Get conversations failed', {
        error: err.message,
        dealershipId
      });

      return [];
    }
  }

  /**
   * Mark messages as read
   */
  async markMessagesAsRead(
    dealershipId: number,
    conversationId: string,
    userId?: number
  ): Promise<{ success: boolean; updatedCount: number }> {
    try {
      // Verify conversation belongs to dealership
      const conversationExists = await db
        .select({ id: conversations.id })
        .from(conversations)
        .where(and(
          eq(conversations.id, conversationId),
          eq(conversations.dealershipId, dealershipId)
        ))
        .limit(1);

      if (conversationExists.length === 0) {
        return { success: false, updatedCount: 0 };
      }

      // Mark unread messages as read
      const updateResult = await db
        .update(messages)
        .set({
          isRead: true,
          readAt: new Date(),
          updatedAt: new Date()
        })
        .where(and(
          eq(messages.conversationId, conversationId),
          eq(messages.isRead, false)
        ))
        .returning({ id: messages.id });

      return { success: true, updatedCount: updateResult.length };

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Mark messages as read failed', {
        error: err.message,
        dealershipId,
        conversationId
      });

      return { success: false, updatedCount: 0 };
    }
  }

  /**
   * Update conversation status
   */
  async updateConversationStatus(
    dealershipId: number,
    conversationId: string,
    status: string,
    userId?: number
  ): Promise<{ success: boolean; conversation?: Conversation }> {
    try {
      const updateResult = await db
        .update(conversations)
        .set({
          status: status as any,
          updatedAt: new Date(),
          closedAt: status === 'resolved' || status === 'archived' ? new Date() : null
        })
        .where(and(
          eq(conversations.id, conversationId),
          eq(conversations.dealershipId, dealershipId)
        ))
        .returning();

      if (updateResult.length === 0) {
        return { success: false };
      }

      const conversation = updateResult[0];

      // Log activity
      await db.insert(leadActivities).values({
        leadId: conversation.leadId,
        userId,
        type: 'conversation_status_changed',
        description: `Conversation status changed to: ${status}`
      });

      return { success: true, conversation };

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Update conversation status failed', {
        error: err.message,
        dealershipId,
        conversationId,
        status
      });

      return { success: false };
    }
  }
}