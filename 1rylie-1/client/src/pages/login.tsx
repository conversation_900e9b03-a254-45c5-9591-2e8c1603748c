import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { useAuth, LoginData } from "@/hooks/useAuth";
import { useEffect } from "react";
import { useLocation } from "wouter";
import { Loader2, LogIn, KeyRound, User, Building2 } from "lucide-react";

export default function LoginPage() {
  const { loginMutation, isAuthenticated, isLoading } = useAuth();
  const [, setLocation] = useLocation();

  // Login form state
  const [loginData, setLoginData] = useState<LoginData>({
    username: "",
    password: "",
  });

  // Function to quickly fill in test account credentials
  const fillTestCredentials = (username: string) => {
    setLoginData({
      username,
      password: "password123",
    });
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setLoginData((prev) => ({ ...prev, [name]: value }));
  };

  // Handle login form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    loginMutation.mutate(loginData);
  };

  useEffect(() => {
    // If user is already authenticated, redirect to dashboard or saved redirect path
    if (isAuthenticated) {
      const redirectPath = localStorage.getItem("redirectAfterLogin") || "/";
      localStorage.removeItem("redirectAfterLogin"); // Clear the stored path
      setLocation(redirectPath);
    }
  }, [isAuthenticated, setLocation]);

  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className="flex h-screen items-center justify-center bg-slate-50 dark:bg-slate-900">
      <Card className="w-full max-w-md shadow-lg">
        <CardHeader className="text-center">
          <CardTitle className="text-2xl font-bold">Welcome to Rylie AI</CardTitle>
          <CardDescription>
            Please log in to access the platform
          </CardDescription>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            {loginMutation.isError && (
              <Alert variant="destructive">
                <AlertDescription>
                  {loginMutation.error instanceof Error
                    ? loginMutation.error.message
                    : "Login failed. Please check your credentials and try again."}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                name="username"
                placeholder="Enter your username"
                value={loginData.username}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                name="password"
                type="password"
                placeholder="Enter your password"
                value={loginData.password}
                onChange={handleInputChange}
                required
              />
            </div>

            <div className="bg-slate-100 dark:bg-slate-800 p-3 rounded-md">
              <h3 className="text-sm font-medium mb-2">Quick Login Options</h3>
              <div className="grid grid-cols-1 gap-2">
                <Button 
                  type="button" 
                  variant="outline" 
                  className="justify-start"
                  onClick={() => fillTestCredentials("superadmin")}
                >
                  <KeyRound className="mr-2 h-4 w-4" />
                  Super Admin
                  <span className="ml-auto text-xs text-muted-foreground">superadmin</span>
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  className="justify-start"
                  onClick={() => fillTestCredentials("luxuryadmin")}
                >
                  <Building2 className="mr-2 h-4 w-4" />
                  Dealership Admin
                  <span className="ml-auto text-xs text-muted-foreground">luxuryadmin</span>
                </Button>
                <Button 
                  type="button" 
                  variant="outline" 
                  className="justify-start"
                  onClick={() => fillTestCredentials("luxurysales")}
                >
                  <User className="mr-2 h-4 w-4" />
                  Sales Representative
                  <span className="ml-auto text-xs text-muted-foreground">luxurysales</span>
                </Button>
              </div>
              <p className="text-xs text-muted-foreground mt-2">
                Click any button above to auto-fill credentials (password is always "password123")
              </p>
            </div>

            <Button
              type="submit"
              className="w-full"
              disabled={loginMutation.isPending}
            >
              {loginMutation.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Logging in...
                </>
              ) : (
                <>
                  <LogIn className="mr-2 h-4 w-4" />
                  Log in
                </>
              )}
            </Button>
          </CardContent>
        </form>
        <CardFooter className="text-center text-sm text-muted-foreground">
          <p>This is a secure service provided by Rylie AI.</p>
        </CardFooter>
      </Card>
    </div>
  );
}