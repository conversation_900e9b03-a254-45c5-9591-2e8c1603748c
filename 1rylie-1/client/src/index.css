@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Modern color palette - soft light mode */
  --background: 210 25% 96%; /* Faded blue background */
  --foreground: 220 20% 20%; /* Soft dark text */

  --card: 0 0% 100%;
  --card-foreground: 220 20% 20%;

  --popover: 0 0% 100%;
  --popover-foreground: 220 20% 20%;

  /* Primary color - deep indigo */
  --primary: 237 60% 55%;
  --primary-foreground: 0 0% 100%;

  /* Secondary color - muted blue-gray */
  --secondary: 230 15% 92%;
  --secondary-foreground: 230 25% 25%;

  --muted: 230 15% 92%;
  --muted-foreground: 230 10% 50%;

  /* Accent color - vibrant blue */
  --accent: 220 80% 60%;
  --accent-foreground: 0 0% 100%;

  /* Alert color */
  --destructive: 0 70% 55%;
  --destructive-foreground: 0 0% 100%;

  --border: 230 15% 90%;
  --input: 230 15% 90%;
  --ring: 230 25% 55%;

  --radius: 0.5rem;
  
  /* Success and warning accent colors */
  --success: 145 65% 40%;
  --success-foreground: 0 0% 100%;
  --warning: 40 90% 50%;
  --warning-foreground: 0 0% 100%;
}

.dark {
  --background: 220 15% 16%; /* Charcoal background */
  --foreground: 220 15% 85%;

  --card: 220 15% 18%;
  --card-foreground: 220 15% 85%;

  --popover: 220 15% 18%;
  --popover-foreground: 220 15% 85%;

  --primary: 237 60% 60%;
  --primary-foreground: 0 0% 100%;

  --secondary: 230 20% 25%;
  --secondary-foreground: 0 0% 100%;

  --muted: 230 20% 25%;
  --muted-foreground: 230 15% 70%;

  --accent: 220 80% 65%;
  --accent-foreground: 0 0% 100%;

  --destructive: 0 70% 50%;
  --destructive-foreground: 0 0% 100%;

  --border: 230 20% 25%;
  --input: 230 20% 25%;
  --ring: 230 15% 70%;
  
  --success: 145 60% 40%;
  --success-foreground: 0 0% 100%;
  --warning: 40 80% 55%;
  --warning-foreground: 0 0% 100%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
  
  /* Typography Hierarchy */
  h1 {
    @apply font-semibold text-2xl tracking-tight;
  }
  h2 {
    @apply font-semibold text-xl tracking-tight;
  }
  h3 {
    @apply font-medium text-lg;
  }
}

/* Increased white space and layout system */
@layer components {
  .container {
    @apply px-6 py-8;
  }
  
  .section {
    @apply my-8;
  }
  
  /* Transition animations */
  .fade-transition {
    @apply transition-all duration-300 ease-in-out;
  }
  
  .slide-transition {
    @apply transition-transform duration-300 ease-in-out;
  }
  
  /* Minimal stylistic components */
  .status-badge {
    @apply inline-flex items-center rounded-full px-2 py-1 text-xs font-medium;
  }
  
  .status-badge-success {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100;
  }
  
  .status-badge-warning {
    @apply bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-100;
  }
  
  .status-badge-error {
    @apply bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100;
  }
  
  .risk-indicator {
    @apply w-2 h-2 rounded-full;
  }
  
  .risk-high {
    @apply bg-red-500;
  }
  
  .risk-medium {
    @apply bg-amber-500;
  }
  
  .risk-low {
    @apply bg-green-500;
  }
  
  /* Collapsible sidebar components */
  .sidebar-icon {
    @apply relative flex items-center justify-center w-12 h-12 mx-auto my-2 rounded-xl 
    text-primary bg-white shadow-md dark:bg-gray-800
    hover:bg-primary hover:text-white
    transition-all duration-300 ease-linear
    cursor-pointer;
  }
  
  .sidebar-tooltip {
    @apply absolute w-auto p-2 m-2 min-w-max left-14 rounded-md shadow-md
    text-white bg-primary
    text-xs font-bold
    transition-all duration-300 scale-0 origin-left;
  }
  
  .sidebar-hr {
    @apply mx-2 my-3 border-t border-gray-200 dark:border-gray-700 opacity-50;
  }
  
  /* Floating action button */
  .floating-action-btn {
    @apply fixed bottom-6 right-6 w-14 h-14 rounded-full bg-primary text-white flex items-center justify-center
    shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out;
  }
}