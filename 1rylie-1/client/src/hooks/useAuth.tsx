import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface User {
  id: number;
  name?: string;
  email: string;
  role: string;
  dealership_id?: number;
}

interface AuthContextType {
  user: User | null;
  loading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// For demo purposes, we'll create a mock user
const MOCK_USER: User = {
  id: 1,
  name: 'Demo User',
  email: '<EMAIL>',
  role: 'admin',
  dealership_id: 1
};

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<User | null>(MOCK_USER); // Pre-set for demo
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // For a real application, we would check authentication here
  useEffect(() => {
    // This would fetch the actual user info
    console.log('Auth provider mounted');
  }, []);

  const login = async (email: string, password: string) => {
    setLoading(true);
    setError(null);
    
    try {
      // In a real app, this would call the API
      setUser(MOCK_USER);
    } catch (err) {
      setError('An error occurred during login');
      throw err;
    } finally {
      setLoading(false);
    }
  };

  const logout = async () => {
    setLoading(true);
    
    try {
      // In a real app, this would call the API
      setUser(null);
    } catch (err) {
      console.error('Logout error', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AuthContext.Provider value={{ user, loading, error, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};