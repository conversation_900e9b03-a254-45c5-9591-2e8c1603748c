// Load environment variables first
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import { createRequire } from 'module';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const require = createRequire(import.meta.url);

dotenv.config();

// Import the db module dynamically
async function runTest() {
  try {
    // Import the db module
    const { default: db, checkDatabaseConnection, executeQuery } = await import('./server/db.js');
    
    console.log('Testing database connection...');
    const isConnected = await checkDatabaseConnection();
    console.log('Database connection test result:', isConnected ? 'CONNECTED' : 'FAILED');
    
    if (isConnected) {
      console.log('Testing executeQuery function...');
      const result = await executeQuery(async () => {
        return { success: true, message: 'Query executed successfully' };
      });
      console.log('Query result:', result);
    }
    
    return isConnected;
  } catch (error) {
    console.error('Error testing database connection:', error);
    console.error(error.stack);
    return false;
  }
}

runTest()
  .then(result => {
    console.log('Test completed with result:', result);
    process.exit(result ? 0 : 1);
  })
  .catch(error => {
    console.error('Unhandled error:', error);
    process.exit(1);
  });