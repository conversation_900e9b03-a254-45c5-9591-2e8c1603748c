You’ll need several **key pieces of information and technical resources from the PureCars team** to successfully stand up and integrate the new <PERSON>ylie backend. Here’s a forward-looking, no-nonsense breakdown of exactly what you’ll need—grouped by category:

---

## 1. **API Integration Details**

**a. Inbound Event Delivery (PureCars → Rylie)**

* **Endpoint Specification:**
  How will PureCars POST customer replies to <PERSON><PERSON><PERSON>? (Payload URL, method, headers)
* **Payload Contract:**
  Exact JSON schema PureCars will send (fields for customer, campaign, message, etc.)
* **Authentication:**
  What auth will PureCars use when calling your `/inbound` API? (API key, OAuth, etc.)
* **Event Triggers:**
  When, specifically, will PureCars POST to <PERSON><PERSON><PERSON>? (e.g., on every customer reply? Batched? Real-time?)

**b. Outbound Delivery (Rylie → PureCars)**

* **Reply API Specification:**
  If <PERSON><PERSON><PERSON> needs to call back to PureCars for delivery/confirmation, what’s the expected endpoint and contract?
* **Email/ADF Details:**
  If <PERSON><PERSON><PERSON> must send lead summaries via email, what’s the required format and delivery method? (Plain email, attachment, ADF, etc.)

---

## 2. **Data & Business Logic**

**a. Inventory Data**

* **Data Source:**
  Where/how does PureCars want to provide dealership inventory?
  (Real-time API, SFTP/CSV feed, periodic batch uploads, direct DB access)
* **Schema:**
  Required fields for each inventory item (e.g., VIN, model, trim, price, features, dealer/store ID)
* **Update Frequency:**
  How often is inventory refreshed? (Live, hourly, daily?)

**b. Persona & Campaign Context**

* **Persona Mapping:**
  How should Rylie determine which persona config to load? (store\_id, campaign\_id, other key?)
* **Custom Arguments:**
  What persona/prompt options are required per store? (Tone, feature emphasis, etc.)
* **Escalation Triggers:**
  What scenarios require handoff to a human? (Compliance, specific keywords, custom business rules)

---

## 3. **Security & Compliance**

* **Allowed Data Scope:**
  What customer data fields can/can’t be stored or used in prompts?
* **PII/Compliance Requirements:**
  Any restrictions on how customer info is handled, logged, or used? (GDPR, CCPA, etc.)
* **API Key/Secret Rotation:**
  Preferred method for exchanging and rotating secrets (shared vault, email, secure portal?)

---

## 4. **Operational & Technical Logistics**

* **Environment URLs:**
  Staging and production endpoint URLs for both PureCars and Rylie.
* **Contact Points:**
  Key technical contacts for API troubleshooting and escalation.
* **SLAs:**
  Response/uptime expectations, notification protocol for outages/incidents.
* **Monitoring/Logging Expectations:**
  Any specific logging, audit, or reporting standards to align with?

---

## 5. **Miscellaneous Integration Needs**

* **Testing/UAT:**
  Sample payloads and sandbox credentials for integration testing.
* **Branding/Persona Inputs:**
  Any brand guidelines, sample conversations, or “voice” documentation for tuning persona configs.
* **Reporting Requirements:**
  Any periodic reporting, metrics, or email summaries needed by the PureCars team.

---

### **Summary Table (What to Request from PureCars)**

| Area            | What to Request                                                                                             |
| --------------- | ----------------------------------------------------------------------------------------------------------- |
| API Integration | - POST URL and payload spec for customer reply events<br>- Authentication details<br>- Outbound/Email specs |
| Data            | - Inventory data source and schema<br>- Persona mapping rules<br>- Escalation triggers/business logic       |
| Security        | - Allowed data fields<br>- PII/compliance rules<br>- API key/secret handling instructions                   |
| Operations      | - Staging/prod URLs<br>- Support contacts<br>- SLA expectations<br>- Monitoring/logging requirements        |
| Miscellaneous   | - Test payloads<br>- Branding/voice docs<br>- Reporting/analytics requirements                              |

---

### **Next Step:**

**Meet with the PureCars team and use this list as your “requirements gathering” checklist.**
Once you have all these details, your engineering team can implement, connect, and go live with Rylie—no guesswork, no dead-ends.

---

Let me know if you want this in a shareable doc or want to expand any section into a stakeholder-facing intake questionnaire.
