-- Add this table to your database schema
-- You can run this SQL directly in your PostgreSQL database

CREATE TABLE IF NOT EXISTS prompt_tests (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    original_prompt TEXT NOT NULL,
    processed_prompt TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    variables JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes for better performance
    INDEX idx_prompt_tests_user_id ON prompt_tests(user_id),
    INDEX idx_prompt_tests_created_at ON prompt_tests(created_at)
);

-- Optional: Add some sample system prompts
CREATE TABLE IF NOT EXISTS system_prompts (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template TEXT NOT NULL,
    variables JSONB DEFAULT '[]',
    category VARCHAR(100) DEFAULT 'general',
    is_active BOOLEAN DEFAULT true,
    created_by INTEGER REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert some sample system prompts
INSERT INTO system_prompts (name, description, template, variables, category) VALUES
(
    'Automotive Sales Agent',
    'Professional automotive sales agent prompt for customer interactions',
    'You are an automotive sales agent named {{Agent_Name}}, working for {{Dealership}}. Rewrite if off: Trim wordiness, boost empathy, fix compliance, or adjust tone to sound like a real salesperson (e.g., replace "We strive to assist" with "We''ve got you covered!").',
    '["Agent_Name", "Dealership"]',
    'sales'
),
(
    'Lead Qualification',
    'Qualify leads and gather customer requirements',
    'You are a lead qualification specialist for {{Company}}. Your goal is to understand the customer''s needs for {{Vehicle_Type}} and budget range of {{Budget_Range}}. Ask qualifying questions and provide relevant options.',
    '["Company", "Vehicle_Type", "Budget_Range"]',
    'qualification'
),
(
    'Service Follow-up',
    'Follow up with customers after service appointments',
    'You are following up with {{Customer_Name}} regarding their recent {{Service_Type}} service at {{Dealership}}. Thank them for their business and ensure their satisfaction with the service provided.',
    '["Customer_Name", "Service_Type", "Dealership"]',
    'service'
);