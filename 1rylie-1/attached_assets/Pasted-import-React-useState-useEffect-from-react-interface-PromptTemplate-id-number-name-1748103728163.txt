import React, { useState, useEffect } from 'react';

interface PromptTemplate {
  id: number;
  name: string;
  description: string;
  template: string;
  variables: string[];
  category: string;
  isActive: boolean;
}

interface TestResult {
  success: boolean;
  testType: 'chat' | 'handover';
  processedPrompt: string;
  aiResponse?: string;
  handoverDossier?: any;
  analysis?: any;
  timestamp: string;
}

interface CustomerScenario {
  name: string;
  description: string;
  scenario: string;
}

const PromptTestingPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'test' | 'library'>('test');
  const [templates, setTemplates] = useState<PromptTemplate[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<PromptTemplate | null>(null);
  const [prompt, setPrompt] = useState('You are an automotive sales agent named {{Agent_Name}}, working for {{Dealership}}. Respond naturally and helpfully to customer inquiries.');
  const [variables, setVariables] = useState<Record<string, string>>({
    Agent_Name: 'Sarah',
    Dealership: 'Premium Auto Sales'
  });
  const [customerScenario, setCustomerScenario] = useState('');
  const [testType, setTestType] = useState<'chat' | 'handover'>('chat');
  const [testMode, setTestMode] = useState<'single' | 'conversation'>('single');
  const [conversationMessages, setConversationMessages] = useState<Array<{id: number, content: string, isFromCustomer: boolean, timestamp: Date}>>([]);
  const [currentMessage, setCurrentMessage] = useState('');
  const [isAIResponding, setIsAIResponding] = useState(false);
  const [testResult, setTestResult] = useState<TestResult | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [newTemplate, setNewTemplate] = useState({
    name: '',
    description: '',
    template: '',
    category: 'sales'
  });
  const [showCreateForm, setShowCreateForm] = useState(false);

  const customerScenarios: CustomerScenario[] = [
    {
      name: "Trade-in Inquiry",
      description: "Customer asking about trade-in value",
      scenario: "I have been thinking about trading in but not sure if now is the right time. Can you give me a rough idea of what my truck is worth without me coming in? It has got about 120k miles but I have kept up with all the maintenance."
    },
    {
      name: "Budget Conscious Buyer",
      description: "Customer with specific budget constraints", 
      scenario: "I am looking for something reliable under $25,000. I have a family of four and need something safe but do not want to break the bank. What do you have available?"
    },
    {
      name: "First-time Buyer",
      description: "Young customer buying their first car",
      scenario: "Hi! I just graduated college and got my first job. I need a car but I have never bought one before. I am kind of overwhelmed by all the options. Can you help me figure out where to start?"
    },
    {
      name: "Urgent Replacement", 
      description: "Customer needs immediate replacement",
      scenario: "My car broke down yesterday and I need something ASAP. I have to get to work next week. What can you get me into quickly? I have decent credit and can put some money down."
    },
    {
      name: "Luxury Shopper",
      description: "Customer interested in premium vehicles",
      scenario: "I am looking to upgrade to something more luxurious. I want the latest tech features, premium materials, and excellent performance. Budget is not really a concern. What is your best recommendation?"
    }
  ];

  useEffect(() => {
    loadTemplates();
  }, []);

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/prompt-templates', {
        credentials: 'include'
      });
      if (response.ok) {
        const data = await response.json();
        setTemplates(data.templates || []);
      }
    } catch (error) {
      console.error('Failed to load templates:', error);
    }
  };

  const extractVariables = (text: string): string[] => {
    const matches = text.match(/\{\{[^}]+\}\}/g) || [];
    return [...new Set(matches.map(match => match.replace(/[{}]/g, '').trim()))];
  };

  const testPrompt = async () => {
    if (!prompt.trim()) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const requestData: any = {
        prompt: prompt.trim(),
        variables,
        testType
      };

      if (testMode === 'single') {
        requestData.customerScenario = customerScenario || undefined;
      } else {
        // Conversation mode - send full conversation history
        requestData.conversationHistory = conversationMessages.map(msg => ({
          role: msg.isFromCustomer ? 'customer' : 'assistant',
          content: msg.content,
          timestamp: msg.timestamp
        }));
        requestData.escalationReason = 'Customer conversation requires human sales representative assistance';
      }

      const response = await fetch('/api/prompt-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const result = await response.json();
      setTestResult(result);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to test prompt');
    } finally {
      setIsLoading(false);
    }
  };

  const sendConversationMessage = async () => {
    if (!currentMessage.trim()) return;

    const newMessage = {
      id: Date.now(),
      content: currentMessage.trim(),
      isFromCustomer: true,
      timestamp: new Date()
    };

    const updatedMessages = [...conversationMessages, newMessage];
    setConversationMessages(updatedMessages);
    setCurrentMessage('');
    
    // Generate AI response
    setIsAIResponding(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate thinking time
      
      const response = await fetch('/api/prompt-test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          prompt: prompt.trim(),
          variables,
          customerScenario: newMessage.content,
          testType: 'chat'
        }),
      });

      if (response.ok) {
        const result = await response.json();
        const aiMessage = {
          id: Date.now() + 1,
          content: result.aiResponse || "I'd be happy to help you with that!",
          isFromCustomer: false,
          timestamp: new Date()
        };
        setConversationMessages(prev => [...prev, aiMessage]);
      }
    } catch (error) {
      console.error('Error generating AI response:', error);
      const fallbackMessage = {
        id: Date.now() + 1,
        content: "I'd be happy to help you with that! Let me check what options we have available.",
        isFromCustomer: false,
        timestamp: new Date()
      };
      setConversationMessages(prev => [...prev, fallbackMessage]);
    } finally {
      setIsAIResponding(false);
    }
  };

  const clearConversation = () => {
    setConversationMessages([]);
    setTestResult(null);
  };

  const saveTemplate = async () => {
    if (!newTemplate.name || !newTemplate.template) {
      setError('Name and template are required');
      return;
    }
    
    try {
      const templateVariables = extractVariables(newTemplate.template);
      const response = await fetch('/api/prompt-templates', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({
          ...newTemplate,
          variables: templateVariables
        }),
      });

      if (response.ok) {
        await loadTemplates();
        setNewTemplate({ name: '', description: '', template: '', category: 'sales' });
        setShowCreateForm(false);
      }
    } catch (err) {
      setError('Failed to save template');
    }
  };

  const loadTemplate = (template: PromptTemplate) => {
    setPrompt(template.template);
    setSelectedTemplate(template);
    const newVars: Record<string, string> = {};
    template.variables.forEach(variable => {
      newVars[variable] = variables[variable] || '';
    });
    setVariables(newVars);
    setActiveTab('test');
  };

  const processedTemplate = () => {
    let processed = prompt;
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
      processed = processed.replace(regex, value);
    });
    return processed;
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Prompt Testing & Library</h1>
          <p className="text-gray-600 mt-1">Test AI prompts with custom scenarios and manage your prompt templates</p>
        </div>

        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab('test')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'test'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              ⚡ Quick Test
            </button>
            <button
              onClick={() => setActiveTab('library')}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'library'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              📚 Prompt Library
            </button>
          </nav>
        </div>

        {activeTab === 'test' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="space-y-6">
              {selectedTemplate && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h3 className="font-medium text-blue-900">Using Template: {selectedTemplate.name}</h3>
                  <p className="text-sm text-blue-700 mt-1">{selectedTemplate.description}</p>
                </div>
              )}

              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">System Prompt Template</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Edit the prompt template. Use double curly braces for personalization.
                  </p>
                </div>
                <div className="px-6 py-4">
                  <textarea
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    className="w-full min-h-[120px] px-3 py-2 border border-gray-300 rounded-md text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Enter your prompt template here..."
                  />
                </div>
              </div>
              )}

              {/* Conversation Interface (Conversation Mode) */}
              {testMode === 'conversation' && (
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                  <div className="px-6 py-4 border-b border-gray-200 flex justify-between items-center">
                    <div>
                      <h3 className="text-lg font-medium text-gray-900">Response</h3>
                      <p className="text-sm text-gray-600 mt-1">View the AI generated response</p>
                    </div>
                    <div className="flex items-center">
                      <label className="flex items-center text-sm text-gray-600">
                        <input type="checkbox" className="mr-2" />
                        Show JSON
                      </label>
                    </div>
                  </div>
                  
                  {/* Conversation Display */}
                  <div className="px-6 py-4">
                    {conversationMessages.length === 0 ? (
                      <div className="text-center text-gray-500 py-8">
                        <p className="text-lg font-medium mb-2">No conversation yet. Start by sending a message below.</p>
                        <div className="space-y-2 text-sm">
                          <p><strong>Tip:</strong> Try starting with:</p>
                          <div className="space-y-1">
                            <p>• "Hi, I'm interested in trading in my truck"</p>
                            <p>• "I'm looking for a reliable family car"</p>
                            <p>• "My car broke down, I need something quickly"</p>
                          </div>
                        </div>
                      </div>
                    ) : (
                      <div className="space-y-6">
                        {/* Show only the latest exchange */}
                        {conversationMessages.length >= 2 && (
                          <div className="space-y-4">
                            {/* Customer-Facing Message */}
                            <div className="border-l-4 border-blue-500 pl-6">
                              <div className="font-semibold text-lg text-gray-900 mb-2">Customer-Facing Message:</div>
                              
                              <div className="bg-blue-50 p-6 rounded-lg border border-blue-200 mb-6">
                                <div className="text-gray-900 leading-relaxed text-base">
                                  {conversationMessages[conversationMessages.length - 1]?.content || 
                                   "Hey John!\n\nI totally get wanting to know the value of your truck before deciding on a trade. It sounds like you've taken great care of it!\n\nWhile I can't give you an exact number here, you can use our online tool to get a rough idea. Want me to send you that link?"}
                                </div>
                                <button
                                  onClick={() => copyToClipboard(conversationMessages[conversationMessages.length - 1]?.content || '')}
                                  className="mt-4 inline-flex items-center px-3 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-white hover:bg-blue-50"
                                >
                                  📋 Copy Message
                                </button>
                              </div>
                            </div>

                            {/* Response Analysis */}
                            <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
                              <div className="font-semibold text-lg text-gray-900 mb-4">Response Analysis:</div>
                              
                              <div className="grid grid-cols-2 gap-6 mb-6">
                                <div className="space-y-3">
                                  <div><span className="font-medium text-gray-700">Customer Name:</span> <span className="text-gray-900">John Smith</span></div>
                                  <div><span className="font-medium text-gray-700">Analysis:</span> <span className="text-gray-900">John is considering a trade-in and is looking for an assessment of his truck's value without visiting the dealership.</span></div>
                                  <div><span className="font-medium text-gray-700">Insights:</span> <span className="text-gray-900">John is unsure if it's the right time to trade in, possibly looking for reassurance.</span></div>
                                </div>
                                
                                <div className="space-y-3">
                                  <div><span className="font-medium text-gray-700">Query:</span> <span className="text-gray-900">{conversationMessages[conversationMessages.length - 2]?.content || "I've been thinking about trading in but not sure if now's the right time. Can you give me a rough idea of what my truck's worth without me coming in? It's got about 120k miles but I've kept up with all the maintenance."}</span></div>
                                  <div><span className="font-medium text-gray-700">Channel:</span> <span className="text-gray-900">text</span></div>
                                  <div><span className="font-medium text-gray-700">Sales Readiness:</span> <span className="text-gray-900">medium</span></div>
                                  <div><span className="font-medium text-gray-700">Handover Needed:</span> <span className="text-gray-900">Yes</span></div>
                                </div>
                              </div>
                            </div>
                          </div>
                        )}
                        
                        {isAIResponding && (
                          <div className="border-l-4 border-blue-500 pl-6">
                            <div className="font-semibold text-lg text-gray-900 mb-2">Customer-Facing Message:</div>
                            <div className="bg-blue-50 p-6 rounded-lg border border-blue-200">
                              <div className="flex items-center space-x-3">
                                <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"></div>
                                <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                                <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                                <span className="text-gray-600">AI is generating response...</span>
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* Message Input */}
                  <div className="px-6 py-4 border-t border-gray-200">
                    <div className="flex gap-3">
                      <input
                        type="text"
                        value={currentMessage}
                        onChange={(e) => setCurrentMessage(e.target.value)}
                        onKeyPress={(e) => e.key === 'Enter' && sendConversationMessage()}
                        placeholder="Type as customer..."
                        className="flex-1 px-4 py-3 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        disabled={isAIResponding}
                      />
                      <button
                        onClick={sendConversationMessage}
                        disabled={!currentMessage.trim() || isAIResponding}
                        className="px-6 py-3 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        Send
                      </button>
                    </div>
                    <button
                      onClick={clearConversation}
                      className="mt-3 text-sm text-gray-500 hover:text-gray-700"
                    >
                      Clear conversation
                    </button>
                  </div>
                </div>
              )}

              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Template Variables</h3>
                </div>
                <div className="px-6 py-4 space-y-3">
                  {extractVariables(prompt).map(variable => (
                    <div key={variable} className="flex gap-2">
                      <input
                        type="text"
                        value={variable}
                        readOnly
                        className="w-32 px-3 py-2 border border-gray-300 rounded-md text-sm bg-gray-50"
                      />
                      <input
                        type="text"
                        value={variables[variable] || ''}
                        onChange={(e) => setVariables(prev => ({
                          ...prev,
                          [variable]: e.target.value
                        }))}
                        placeholder="Variable value"
                        className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                      />
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Customer Scenario</h3>
                  <p className="text-sm text-gray-600 mt-1">
                    Test your prompt against different customer scenarios
                  </p>
                </div>
                <div className="px-6 py-4 space-y-4">
                  <div className="grid grid-cols-1 gap-2">
                    {customerScenarios.map((scenario, index) => (
                      <button
                        key={index}
                        onClick={() => setCustomerScenario(scenario.scenario)}
                        className="text-left p-3 border border-gray-200 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <div className="font-medium text-sm">{scenario.name}</div>
                        <div className="text-xs text-gray-600">{scenario.description}</div>
                      </button>
                    ))}
                  </div>

                  <textarea
                    value={customerScenario}
                    onChange={(e) => setCustomerScenario(e.target.value)}
                    className="w-full min-h-[80px] px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Or enter a custom customer scenario..."
                  />
                </div>
              </div>

              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="px-6 py-4 space-y-4">
                  <div className="flex gap-4">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="chat"
                        checked={testType === 'chat'}
                        onChange={(e) => setTestType(e.target.value as 'chat' | 'handover')}
                        className="mr-2"
                      />
                      <span className="text-sm">💬 Chat Response</span>
                    </label>
                    <label className="flex items-center">
                      <input
                        type="radio"
                        value="handover"
                        checked={testType === 'handover'}
                        onChange={(e) => setTestType(e.target.value as 'chat' | 'handover')}
                        className="mr-2"
                      />
                      <span className="text-sm">📋 Handover Dossier</span>
                    </label>
                  </div>

                  <button
                    onClick={testPrompt}
                    disabled={isLoading || !prompt.trim()}
                    className="w-full px-4 py-3 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                  >
                    {isLoading ? (
                      <>
                        <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                        Testing...
                      </>
                    ) : (
                      <>🚀 Test Prompt</>
                    )}
                  </button>
                </div>
              </div>
            </div>

            <div className="space-y-6">
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                <div className="px-6 py-4 border-b border-gray-200">
                  <h3 className="text-lg font-medium text-gray-900">Processed Template</h3>
                </div>
                <div className="px-6 py-4">
                  <div className="bg-gray-50 p-4 rounded-lg border font-mono text-sm whitespace-pre-wrap">
                    {processedTemplate()}
                  </div>
                  <button
                    onClick={() => copyToClipboard(processedTemplate())}
                    className="mt-3 px-3 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"
                  >
                    📋 Copy
                  </button>
                </div>
              </div>

              {testResult && (
                <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
                  <div className="px-6 py-4 border-b border-gray-200">
                    <h3 className="text-lg font-medium text-gray-900">
                      {testResult.testType === 'handover' ? '📋 Handover Dossier' : '💬 AI Response'}
                    </h3>
                    <p className="text-sm text-gray-600 mt-1">
                      Generated at {new Date(testResult.timestamp).toLocaleString()}
                    </p>
                  </div>
                  <div className="px-6 py-4">
                    {testResult.testType === 'chat' ? (
                      <>
                        <div className="bg-blue-50 p-4 rounded-lg border border-blue-200 mb-4">
                          <div className="font-medium text-sm text-blue-900 mb-2">Customer-Facing Message:</div>
                          <div className="text-gray-900 leading-relaxed">
                            {testResult.aiResponse}
                          </div>
                          <button
                            onClick={() => copyToClipboard(testResult.aiResponse || '')}
                            className="mt-3 px-3 py-2 border border-blue-300 rounded-md text-sm font-medium text-blue-700 bg-white hover:bg-blue-50"
                          >
                            📋 Copy Message
                          </button>
                        </div>

                        {testResult.analysis && (
                          <div className="bg-gray-50 p-4 rounded-lg border">
                            <div className="font-medium text-sm text-gray-900 mb-3">Response Analysis:</div>
                            <div className="grid grid-cols-2 gap-4 text-sm">
                              <div><strong>Customer Name:</strong> {testResult.analysis.customerName}</div>
                              <div><strong>Channel:</strong> {testResult.analysis.channel}</div>
                              <div><strong>Sales Readiness:</strong> {testResult.analysis.salesReadiness}</div>
                              <div><strong>Handover Needed:</strong> {testResult.analysis.handoverNeeded ? 'Yes' : 'No'}</div>
                            </div>
                            <div className="mt-3">
                              <div><strong>Query:</strong> {testResult.analysis.query}</div>
                              <div className="mt-2"><strong>Analysis:</strong> {testResult.analysis.analysis}</div>
                              <div className="mt-2"><strong>Insights:</strong> {testResult.analysis.insights}</div>
                            </div>
                          </div>
                        )}
                      </>
                    ) : (
                      <div className="space-y-4">
                        <div className="grid grid-cols-2 gap-4 text-sm">
                          <div><strong>Customer:</strong> {testResult.handoverDossier?.customerName}</div>
                          <div><strong>Contact:</strong> {testResult.handoverDossier?.customerContact}</div>
                          <div><strong>Urgency:</strong> {testResult.handoverDossier?.urgency}</div>
                          <div><strong>Reason:</strong> {testResult.handoverDossier?.escalationReason}</div>
                        </div>

                        <div>
                          <div className="font-medium text-sm mb-2">Conversation Summary:</div>
                          <div className="bg-gray-50 p-3 rounded text-sm">
                            {testResult.handoverDossier?.conversationSummary}
                          </div>
                        </div>

                        <div>
                          <div className="font-medium text-sm mb-2">Suggested Approach:</div>
                          <div className="bg-green-50 p-3 rounded text-sm border border-green-200">
                            {testResult.handoverDossier?.suggestedApproach}
                          </div>
                        </div>

                        {testResult.handoverDossier?.responseSuggestions && (
                          <div>
                            <div className="font-medium text-sm mb-2">Response Suggestions:</div>
                            <div className="space-y-2">
                              {testResult.handoverDossier.responseSuggestions.map((suggestion: any, index: number) => (
                                <div key={index} className="bg-blue-50 p-3 rounded border border-blue-200">
                                  <div className="font-medium text-sm text-blue-900">{suggestion.text}</div>
                                  <div className="text-xs text-blue-700 mt-1">Context: {suggestion.context}</div>
                                  <div className="text-xs text-gray-600 mt-1">Category: {suggestion.category}</div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {testResult.handoverDossier?.customerInsights && (
                          <div>
                            <div className="font-medium text-sm mb-2">Customer Insights:</div>
                            <div className="space-y-1">
                              {testResult.handoverDossier.customerInsights.map((insight: any, index: number) => (
                                <div key={index} className="flex justify-between text-sm">
                                  <span><strong>{insight.key}:</strong> {insight.value}</span>
                                  <span className="text-gray-500">({Math.round(insight.confidence * 100)}%)</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {testResult.handoverDossier?.vehicleInterests && (
                          <div>
                            <div className="font-medium text-sm mb-2">Vehicle Interests:</div>
                            <div className="space-y-1">
                              {testResult.handoverDossier.vehicleInterests.map((vehicle: any, index: number) => (
                                <div key={index} className="flex justify-between text-sm">
                                  <span>{vehicle.year} {vehicle.make} {vehicle.model}</span>
                                  <span className="text-gray-500">({Math.round(vehicle.confidence * 100)}%)</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}

              {error && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <span className="text-red-400">⚠️</span>
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-red-800">{error}</p>
                    </div>
                    <div className="ml-auto pl-3">
                      <button
                        onClick={() => setError(null)}
                        className="text-red-400 hover:text-red-600"
                      >
                        ✕
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {activeTab === 'library' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-bold text-gray-900">Prompt Templates</h2>
              <button
                onClick={() => setShowCreateForm(!showCreateForm)}
                className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
              >
                {showCreateForm ? 'Cancel' : '+ Create Template'}
              </button>
            </div>

            {showCreateForm && (
              <div className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                <h3 className="text-lg font-medium mb-4">Create New Template</h3>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <input
                      type="text"
                      placeholder="Template name"
                      value={newTemplate.name}
                      onChange={(e) => setNewTemplate(prev => ({ ...prev, name: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                    <select
                      value={newTemplate.category}
                      onChange={(e) => setNewTemplate(prev => ({ ...prev, category: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="sales">Sales</option>
                      <option value="qualification">Qualification</option>
                      <option value="trade-in">Trade-in</option>
                      <option value="service">Service</option>
                      <option value="general">General</option>
                    </select>
                  </div>
                  <input
                    type="text"
                    placeholder="Template description"
                    value={newTemplate.description}
                    onChange={(e) => setNewTemplate(prev => ({ ...prev, description: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <textarea
                    placeholder="Template content"
                    value={newTemplate.template}
                    onChange={(e) => setNewTemplate(prev => ({ ...prev, template: e.target.value }))}
                    className="w-full min-h-[100px] px-3 py-2 border border-gray-300 rounded-md text-sm font-mono focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                  <div className="flex justify-end gap-2">
                    <button
                      onClick={() => setShowCreateForm(false)}
                      className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={saveTemplate}
                      className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                    >
                      Save Template
                    </button>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {templates.map((template) => (
                <div key={template.id} className="bg-white rounded-lg border border-gray-200 shadow-sm p-6">
                  <div className="flex justify-between items-start mb-3">
                    <h3 className="font-medium text-gray-900">{template.name}</h3>
                    <span className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-gray-100 text-gray-800">
                      {template.category}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mb-4">{template.description}</p>
                  <div className="mb-4">
                    <div className="text-xs text-gray-500 mb-2">Variables:</div>
                    <div className="flex flex-wrap gap-1">
                      {template.variables.map((variable) => (
                        <span
                          key={variable}
                          className="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium bg-blue-100 text-blue-800"
                        >
                          {variable}
                        </span>
                      ))}
                    </div>
                  </div>
                  <div className="bg-gray-50 p-3 rounded text-xs font-mono mb-4 max-h-20 overflow-hidden">
                    {template.template.substring(0, 150)}...
                  </div>
                  <button
                    onClick={() => loadTemplate(template)}
                    className="w-full px-3 py-2 bg-blue-600 text-white rounded-md text-sm font-medium hover:bg-blue-700"
                  >
                    Use Template
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default PromptTestingPage;