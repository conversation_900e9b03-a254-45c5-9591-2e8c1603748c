/**
 * Improved cache service with proper TypeScript types and error handling
 */

interface CacheItem<T> {
  value: T;
  expiry: number | null;
}

interface CacheOptions {
  ttl?: number; // Time to live in seconds
}

interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: string;
}

class MemoryCache {
  private cache: Map<string, CacheItem<any>>;
  private hits: number;
  private misses: number;

  constructor() {
    this.cache = new Map();
    this.hits = 0;
    this.misses = 0;
  }

  get<T>(key: string): T | null {
    const item = this.cache.get(key);
    
    if (!item) {
      this.misses++;
      return null;
    }

    // Check if item has expired
    if (item.expiry && item.expiry < Date.now()) {
      this.delete(key);
      this.misses++;
      return null;
    }

    this.hits++;
    return item.value as T;
  }

  set<T>(key: string, value: T, options: CacheOptions = {}): void {
    const ttlMs = (options.ttl || 3600) * 1000; // Convert seconds to milliseconds
    const expiry = ttlMs > 0 ? Date.now() + ttlMs : null;
    
    this.cache.set(key, {
      value,
      expiry
    });
  }

  delete(key: string): boolean {
    return this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
    this.hits = 0;
    this.misses = 0;
  }

  // Pattern-based invalidation
  invalidatePattern(pattern: string): void {
    const keys = Array.from(this.cache.keys());
    keys.forEach(key => {
      if (key.includes(pattern)) {
        this.delete(key);
      }
    });
  }

  // Cache-aside pattern implementation
  async getOrSet<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = this.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, fetch the data
    const value = await fetchFn();
    
    // Store in cache
    this.set(key, value, options);
    
    return value;
  }

  getStats(): CacheStats {
    const total = this.hits + this.misses;
    const hitRate = total > 0 ? (this.hits / total) * 100 : 0;
    
    return {
      size: this.cache.size,
      hits: this.hits,
      misses: this.misses,
      hitRate: `${hitRate.toFixed(2)}%`
    };
  }
}

// Create a singleton instance
const memoryCache = new MemoryCache();

// Helper function to create consistent cache keys
export function createCacheKey(prefix: string, params: Record<string, any>): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  
  return `${prefix}:${sortedParams}`;
}

// Export the cache service and utility functions
export const cacheService = {
  get: <T>(key: string) => memoryCache.get<T>(key),
  set: <T>(key: string, value: T, options?: CacheOptions) => memoryCache.set(key, value, options),
  delete: (key: string) => memoryCache.delete(key),
  clear: () => memoryCache.clear(),
  invalidatePattern: (pattern: string) => memoryCache.invalidatePattern(pattern),
  getOrSet: <T>(key: string, fetchFn: () => Promise<T>, options?: CacheOptions) => 
    memoryCache.getOrSet(key, fetchFn, options),
  getStats: () => memoryCache.getStats()
};

export const getCacheStats = (): CacheStats => {
  return memoryCache.getStats();
};

export const shutdownCache = async (): Promise<void> => {
  memoryCache.clear();
  console.log('Cache cleared successfully');
  return Promise.resolve();
};

export default cacheService;