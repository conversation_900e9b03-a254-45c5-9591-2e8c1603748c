-- Add dual-mode support to existing schema
-- Run this script to add the required tables and columns

-- 1. Add new columns to dealerships table
ALTER TABLE dealerships 
ADD COLUMN IF NOT EXISTS operation_mode VARCHAR(50) DEFAULT 'rylie_ai',
ADD COLUMN IF NOT EXISTS ai_config JSONB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS agent_config J<PERSON>NB DEFAULT '{}',
ADD COLUMN IF NOT EXISTS lead_routing JSONB DEFAULT '{}';

-- 2. Create message templates table
CREATE TABLE IF NOT EXISTS message_templates (
    id VARCHAR(255) PRIMARY KEY,
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    name VA<PERSON>HAR(255) NOT NULL,
    subject VARCHAR(500),
    content TEXT NOT NULL,
    variables JSONB DEFAULT '[]',
    channel VARCHAR(50) NOT NULL DEFAULT 'both', -- 'email', 'sms', 'both'
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 3. Create follow-up rules table
CREATE TABLE IF NOT EXISTS follow_up_rules (
    id VARCHAR(255) PRIMARY KEY,
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    lead_source VARCHAR(255) NOT NULL,
    sequence JSONB NOT NULL DEFAULT '[]',
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 4. Create lead sources table
CREATE TABLE IF NOT EXISTS lead_sources (
    id VARCHAR(255) PRIMARY KEY,
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL, -- 'website', 'phone', 'email', 'chat', 'social', 'referral', 'other'
    base_score INTEGER DEFAULT 50,
    follow_up_rule_id VARCHAR(255) REFERENCES follow_up_rules(id),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 5. Create leads table
CREATE TABLE IF NOT EXISTS leads (
    id BIGINT PRIMARY KEY,
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    conversation_id INTEGER REFERENCES conversations(id),
    source VARCHAR(255) NOT NULL,
    status VARCHAR(50) DEFAULT 'new', -- 'new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'
    priority VARCHAR(50) DEFAULT 'medium', -- 'low', 'medium', 'high', 'urgent'
    
    -- Customer information
    customer_name VARCHAR(255),
    customer_email VARCHAR(255),
    customer_phone VARCHAR(100),
    
    -- Lead details
    interested_vehicle VARCHAR(255),
    price_range VARCHAR(100),
    timeframe VARCHAR(100),
    tradein BOOLEAN DEFAULT FALSE,
    financing BOOLEAN DEFAULT FALSE,
    
    -- Scoring
    lead_score INTEGER DEFAULT 50,
    score_factors JSONB DEFAULT '{}',
    
    -- Tracking
    first_contact_at TIMESTAMP,
    last_contact_at TIMESTAMP,
    next_follow_up_at TIMESTAMP,
    follow_up_count INTEGER DEFAULT 0,
    response_count INTEGER DEFAULT 0,
    
    -- Metadata
    notes TEXT,
    assigned_to INTEGER REFERENCES users(id),
    tags JSONB DEFAULT '[]',
    custom_fields JSONB DEFAULT '{}',
    
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 6. Create lead scoring rules table
CREATE TABLE IF NOT EXISTS lead_scoring_rules (
    id VARCHAR(255) PRIMARY KEY,
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    conditions JSONB NOT NULL DEFAULT '[]',
    score INTEGER NOT NULL,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- 7. Create scheduled messages table
CREATE TABLE IF NOT EXISTS scheduled_messages (
    id VARCHAR(255) PRIMARY KEY,
    conversation_id INTEGER REFERENCES conversations(id),
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    lead_id BIGINT REFERENCES leads(id),
    channel VARCHAR(50) NOT NULL, -- 'email', 'sms'
    recipient VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    subject VARCHAR(500),
    scheduled_for TIMESTAMP NOT NULL,
    sent BOOLEAN DEFAULT FALSE,
    sent_at TIMESTAMP,
    error TEXT,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 8. Create message logs table
CREATE TABLE IF NOT EXISTS message_logs (
    id BIGSERIAL PRIMARY KEY,
    conversation_id INTEGER REFERENCES conversations(id),
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    lead_id BIGINT REFERENCES leads(id),
    channel VARCHAR(50) NOT NULL,
    recipient VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    subject VARCHAR(500),
    success BOOLEAN NOT NULL,
    error_message TEXT,
    sent_at TIMESTAMP DEFAULT NOW()
);

-- 9. Create agent queue table
CREATE TABLE IF NOT EXISTS agent_queue (
    id BIGSERIAL PRIMARY KEY,
    conversation_id INTEGER NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
    dealership_id INTEGER NOT NULL REFERENCES dealerships(id) ON DELETE CASCADE,
    priority VARCHAR(50) DEFAULT 'normal', -- 'low', 'normal', 'high', 'urgent'
    queued_at TIMESTAMP DEFAULT NOW(),
    assigned_at TIMESTAMP,
    assigned_to INTEGER REFERENCES users(id),
    resolved_at TIMESTAMP
);

-- 10. Create interaction logs table
CREATE TABLE IF NOT EXISTS interaction_logs (
    id BIGSERIAL PRIMARY KEY,
    lead_id BIGINT NOT NULL REFERENCES leads(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'response', 'contact', 'call', 'email', 'sms'
    description TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP DEFAULT NOW()
);

-- 11. Add indexes for performance
CREATE INDEX IF NOT EXISTS idx_dealerships_operation_mode ON dealerships(operation_mode);
CREATE INDEX IF NOT EXISTS idx_message_templates_dealership ON message_templates(dealership_id, active);
CREATE INDEX IF NOT EXISTS idx_follow_up_rules_dealership_source ON follow_up_rules(dealership_id, lead_source, active);
CREATE INDEX IF NOT EXISTS idx_lead_sources_dealership ON lead_sources(dealership_id, active);

CREATE INDEX IF NOT EXISTS idx_leads_dealership_status ON leads(dealership_id, status);
CREATE INDEX IF NOT EXISTS idx_leads_dealership_priority ON leads(dealership_id, priority);
CREATE INDEX IF NOT EXISTS idx_leads_source ON leads(source);
CREATE INDEX IF NOT EXISTS idx_leads_assigned_to ON leads(assigned_to);
CREATE INDEX IF NOT EXISTS idx_leads_created_at ON leads(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_leads_next_followup ON leads(next_follow_up_at) WHERE next_follow_up_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_scheduled_messages_due ON scheduled_messages(scheduled_for) WHERE sent = FALSE;
CREATE INDEX IF NOT EXISTS idx_scheduled_messages_dealership ON scheduled_messages(dealership_id, sent);

CREATE INDEX IF NOT EXISTS idx_message_logs_dealership_date ON message_logs(dealership_id, sent_at DESC);
CREATE INDEX IF NOT EXISTS idx_message_logs_success ON message_logs(success, sent_at DESC);

CREATE INDEX IF NOT EXISTS idx_agent_queue_dealership ON agent_queue(dealership_id, queued_at);
CREATE INDEX IF NOT EXISTS idx_agent_queue_assigned ON agent_queue(assigned_to, assigned_at) WHERE assigned_to IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_interaction_logs_lead ON interaction_logs(lead_id, created_at DESC);

-- 12. Add constraints
ALTER TABLE leads ADD CONSTRAINT chk_leads_status 
CHECK (status IN ('new', 'contacted', 'qualified', 'unqualified', 'converted', 'lost'));

ALTER TABLE leads ADD CONSTRAINT chk_leads_priority 
CHECK (priority IN ('low', 'medium', 'high', 'urgent'));

ALTER TABLE dealerships ADD CONSTRAINT chk_dealerships_operation_mode 
CHECK (operation_mode IN ('rylie_ai', 'direct_agent'));

-- 13. Add some default data
INSERT INTO lead_scoring_rules (id, dealership_id, name, conditions, score, active)
SELECT 
    'default_email_rule_' || id,
    id,
    'Has Email Address',
    '[{"field": "customerEmail", "operator": "contains", "value": "@", "weight": 1}]',
    10,
    true
FROM dealerships
ON CONFLICT (id) DO NOTHING;

INSERT INTO lead_scoring_rules (id, dealership_id, name, conditions, score, active)
SELECT 
    'default_phone_rule_' || id,
    id,
    'Has Phone Number',
    '[{"field": "customerPhone", "operator": "contains", "value": "", "weight": 1}]',
    15,
    true
FROM dealerships
ON CONFLICT (id) DO NOTHING;

-- 14. Update existing conversations to have a default source if null
UPDATE conversations 
SET source = 'legacy_import' 
WHERE source IS NULL OR source = '';

-- 15. Create a trigger to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply the trigger to relevant tables
CREATE TRIGGER update_dealerships_updated_at 
    BEFORE UPDATE ON dealerships 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_leads_updated_at 
    BEFORE UPDATE ON leads 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_message_templates_updated_at 
    BEFORE UPDATE ON message_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_follow_up_rules_updated_at 
    BEFORE UPDATE ON follow_up_rules 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 16. Add comments for documentation
COMMENT ON TABLE message_templates IS 'Templates for automated messages in direct agent mode';
COMMENT ON TABLE follow_up_rules IS 'Rules for automated follow-up sequences based on lead source';
COMMENT ON TABLE leads IS 'Lead tracking and management for both AI and direct agent modes';
COMMENT ON TABLE scheduled_messages IS 'Queue for delayed message delivery';
COMMENT ON TABLE agent_queue IS 'Queue for conversations waiting for agent assignment';

COMMENT ON COLUMN dealerships.operation_mode IS 'Operating mode: rylie_ai or direct_agent';
COMMENT ON COLUMN dealerships.ai_config IS 'Configuration for AI mode (PureCars API settings, etc.)';
COMMENT ON COLUMN dealerships.agent_config IS 'Configuration for direct agent mode (working hours, templates, etc.)';
COMMENT ON COLUMN dealerships.lead_routing IS 'Lead routing and automation settings';

-- Script completion
SELECT 'Dual-mode schema update completed successfully!' as status;