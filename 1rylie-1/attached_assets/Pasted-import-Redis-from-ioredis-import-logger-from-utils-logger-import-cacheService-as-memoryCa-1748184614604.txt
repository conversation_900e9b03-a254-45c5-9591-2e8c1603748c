import Redis from 'ioredis';
import logger from '../utils/logger';
import { cacheService as memoryCacheService } from './cache-service';

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  prefix?: string; // Optional key prefix
}

interface CacheStats {
  size: number;
  hits: number;
  misses: number;
  hitRate: string;
  type: 'redis' | 'memory';
  connected: boolean;
  info?: any;
}

class RedisCacheService {
  private redis: Redis | null = null;
  private fallbackToMemory: boolean = false;
  private hits: number = 0;
  private misses: number = 0;
  private keyPrefix: string = 'rylie:';

  constructor() {
    this.initializeRedis();
  }

  private async initializeRedis(): Promise<void> {
    // Skip Redis initialization if explicitly disabled
    if (process.env.SKIP_REDIS === 'true') {
      logger.info('Redis disabled via environment variable, using memory cache');
      this.fallbackToMemory = true;
      return;
    }

    try {
      const redisConfig = {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379'),
        password: process.env.REDIS_PASSWORD,
        db: parseInt(process.env.REDIS_DB || '0'),
        retryDelayOnFailover: 100,
        maxRetriesPerRequest: 3,
        lazyConnect: true,
        connectTimeout: 5000,
        commandTimeout: 5000,
      };

      this.redis = new Redis(redisConfig);

      this.redis.on('connect', () => {
        logger.info('Redis connected successfully', {
          host: redisConfig.host,
          port: redisConfig.port,
          db: redisConfig.db
        });
        this.fallbackToMemory = false;
      });

      this.redis.on('error', (error) => {
        logger.error('Redis connection error', { error: error.message });
        this.fallbackToMemory = true;
      });

      this.redis.on('close', () => {
        logger.warn('Redis connection closed, falling back to memory cache');
        this.fallbackToMemory = true;
      });

      this.redis.on('reconnecting', () => {
        logger.info('Redis reconnecting...');
      });

      // Test the connection
      await this.redis.ping();
      logger.info('Redis ping successful');
      
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Failed to initialize Redis, using memory cache fallback', { 
        error: err.message 
      });
      this.fallbackToMemory = true;
    }
  }

  private isRedisAvailable(): boolean {
    return this.redis !== null && 
           !this.fallbackToMemory && 
           this.redis.status === 'ready';
  }

  private buildKey(key: string, prefix?: string): string {
    const finalPrefix = prefix || this.keyPrefix;
    return `${finalPrefix}${key}`;
  }

  async get<T>(key: string, options: CacheOptions = {}): Promise<T | null> {
    try {
      if (this.isRedisAvailable() && this.redis) {
        const redisKey = this.buildKey(key, options.prefix);
        const value = await this.redis.get(redisKey);
        
        if (value !== null) {
          this.hits++;
          try {
            return JSON.parse(value) as T;
          } catch {
            // If JSON parsing fails, return as string
            return value as unknown as T;
          }
        }
        this.misses++;
        return null;
      } else {
        // Fallback to memory cache
        const result = memoryCacheService.get<T>(key);
        if (result !== null) {
          this.hits++;
        } else {
          this.misses++;
        }
        return result;
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Redis get error, falling back to memory cache', { 
        error: err.message, 
        key 
      });
      this.fallbackToMemory = true;
      return memoryCacheService.get<T>(key);
    }
  }

  async set<T>(key: string, value: T, options: CacheOptions = {}): Promise<void> {
    try {
      if (this.isRedisAvailable() && this.redis) {
        const redisKey = this.buildKey(key, options.prefix);
        const ttl = options.ttl || 3600; // Default 1 hour
        const serializedValue = typeof value === 'string' ? value : JSON.stringify(value);
        
        await this.redis.setex(redisKey, ttl, serializedValue);
      } else {
        // Fallback to memory cache
        memoryCacheService.set(key, value, options);
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Redis set error, falling back to memory cache', { 
        error: err.message, 
        key 
      });
      this.fallbackToMemory = true;
      memoryCacheService.set(key, value, options);
    }
  }

  async delete(key: string, options: CacheOptions = {}): Promise<boolean> {
    try {
      if (this.isRedisAvailable() && this.redis) {
        const redisKey = this.buildKey(key, options.prefix);
        const result = await this.redis.del(redisKey);
        return result > 0;
      } else {
        // Fallback to memory cache
        return memoryCacheService.delete(key);
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Redis delete error, falling back to memory cache', { 
        error: err.message, 
        key 
      });
      this.fallbackToMemory = true;
      return memoryCacheService.delete(key);
    }
  }

  async clear(prefix?: string): Promise<void> {
    try {
      if (this.isRedisAvailable() && this.redis) {
        const pattern = prefix ? this.buildKey('*', prefix) : `${this.keyPrefix}*`;
        const keys = await this.redis.keys(pattern);
        
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      }
      
      // Also clear memory cache
      memoryCacheService.clear();
      this.hits = 0;
      this.misses = 0;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Redis clear error', { error: err.message });
      memoryCacheService.clear();
    }
  }

  async invalidatePattern(pattern: string, options: CacheOptions = {}): Promise<void> {
    try {
      if (this.isRedisAvailable() && this.redis) {
        const searchPattern = this.buildKey(`*${pattern}*`, options.prefix);
        const keys = await this.redis.keys(searchPattern);
        
        if (keys.length > 0) {
          await this.redis.del(...keys);
          logger.info(`Invalidated ${keys.length} Redis keys matching pattern: ${pattern}`);
        }
      }
      
      // Also invalidate in memory cache
      memoryCacheService.invalidatePattern(pattern);
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Redis pattern invalidation error', { 
        error: err.message, 
        pattern 
      });
      memoryCacheService.invalidatePattern(pattern);
    }
  }

  async getOrSet<T>(
    key: string, 
    fetchFn: () => Promise<T>, 
    options: CacheOptions = {}
  ): Promise<T> {
    // Try to get from cache first
    const cached = await this.get<T>(key, options);
    if (cached !== null) {
      return cached;
    }

    // If not in cache, fetch the data
    const value = await fetchFn();
    
    // Store in cache
    await this.set(key, value, options);
    
    return value;
  }

  async getStats(): Promise<CacheStats> {
    const total = this.hits + this.misses;
    const hitRate = total > 0 ? (this.hits / total) * 100 : 0;
    
    if (this.isRedisAvailable() && this.redis) {
      try {
        const info = await this.redis.info('memory');
        const dbsize = await this.redis.dbsize();
        
        return {
          size: dbsize,
          hits: this.hits,
          misses: this.misses,
          hitRate: `${hitRate.toFixed(2)}%`,
          type: 'redis',
          connected: true,
          info: {
            memory: info,
            dbsize
          }
        };
      } catch (error) {
        // Fallback to basic stats if info commands fail
        return {
          size: -1,
          hits: this.hits,
          misses: this.misses,
          hitRate: `${hitRate.toFixed(2)}%`,
          type: 'redis',
          connected: true
        };
      }
    } else {
      const memStats = memoryCacheService.getStats();
      return {
        ...memStats,
        type: 'memory',
        connected: false
      };
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      if (this.isRedisAvailable() && this.redis) {
        await this.redis.ping();
        return true;
      }
      return false;
    } catch {
      return false;
    }
  }

  async shutdown(): Promise<void> {
    try {
      if (this.redis) {
        await this.redis.quit();
        logger.info('Redis connection closed successfully');
      }
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error closing Redis connection', { error: err.message });
    }
  }
}

// Create singleton instance
const redisCacheService = new RedisCacheService();

// Export the enhanced cache service (replaces the memory-only version)
export const cacheService = {
  get: <T>(key: string, options?: CacheOptions) => redisCacheService.get<T>(key, options),
  set: <T>(key: string, value: T, options?: CacheOptions) => redisCacheService.set(key, value, options),
  delete: (key: string, options?: CacheOptions) => redisCacheService.delete(key, options),
  clear: (prefix?: string) => redisCacheService.clear(prefix),
  invalidatePattern: (pattern: string, options?: CacheOptions) => redisCacheService.invalidatePattern(pattern, options),
  getOrSet: <T>(key: string, fetchFn: () => Promise<T>, options?: CacheOptions) => 
    redisCacheService.getOrSet(key, fetchFn, options),
  getStats: () => redisCacheService.getStats(),
  healthCheck: () => redisCacheService.healthCheck(),
  shutdown: () => redisCacheService.shutdown()
};

export { createCacheKey } from './cache-service';
export default cacheService;