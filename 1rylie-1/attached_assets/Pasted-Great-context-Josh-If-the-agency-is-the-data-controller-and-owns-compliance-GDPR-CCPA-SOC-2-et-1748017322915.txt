Great context, Josh. If the agency is the data controller and owns compliance (GDPR, CCPA, SOC 2, etc.), my focus as CEO/CTO now shifts toward risk mitigation, operational resilience, and technical fit. Here are 15 targeted, C-level questions I’d want answered before moving forward with Rylie AI—or any solution in this space. These go beyond boilerplate, digging into areas that actually surface in technical and commercial due diligence.

⸻

15 Critical Questions for Rylie AI Evaluation

Security
	1.	API Key Security:
How are dealership API keys generated, managed, rotated, and revoked? What controls are in place to prevent leakage or misuse?
	2.	Transport Security:
Can you provide evidence (or documentation) of enforced HTTPS on all endpoints—including integrations, webhooks, and internal APIs?
	3.	Input Validation & Attack Prevention:
What measures are in place to prevent injection attacks, malformed payloads, or denial-of-service via API abuse?
	4.	Data Encryption:
Describe the encryption methods used for customer data at rest and in transit. How do you handle encryption key management?
	5.	Access Controls:
How is access to sensitive operations (such as prompt/config modifications or handover dossiers) managed and logged within your admin tools?

Reliability
	6.	Uptime and Incident Transparency:
What is your real-world uptime (past 12 months) and how do you communicate major incidents or outages to partners?
	7.	Rate Limiting & Abuse Prevention:
How does your rate limiting adapt to legitimate traffic spikes (e.g., campaigns), and what are the thresholds for blocking or throttling?
	8.	Error Handling & Recovery:
What happens if a message fails processing (e.g., LLM downtime, malformed payload)? How are errors surfaced, logged, and retried?
	9.	Disaster Recovery:
What is your disaster recovery plan—including backup frequency, offsite storage, and restore SLAs?
	10.	Monitoring & Alerting:
What monitoring is in place for API latency, failed handovers, and degraded response times? How are these surfaced to your engineering team (and optionally, to partners)?

Scalability
	11.	Load Handling:
What is the platform’s tested throughput (messages per second, concurrent sessions) and how do you ensure low latency under peak load?
	12.	Multi-Tenant Isolation:
How do you ensure complete data and config isolation between dealerships, especially as you scale to dozens or hundreds of clients?
	13.	Elasticity:
Can the platform dynamically scale up or down based on usage (e.g., sudden influx of customer messages)? Is this automated or manual?
	14.	Versioning and Backward Compatibility:
How do you roll out updates to APIs, prompt logic, or payload formats without breaking existing integrations? What’s your rollback strategy?
	15.	Integration Roadmap:
How do you approach adding new channels (WhatsApp, Messenger, etc.) or changing existing third-party dependencies to minimize disruption for partners?

⸻

If I don’t get clear, actionable answers (and preferably proof) on these points, we’re not moving forward.
These questions are specifically tailored for Rylie AI’s described architecture and the context of a large agency owning compliance but demanding operational excellence from its tech vendors.

If you want these reframed for an RFP, or want a “red flag” checklist (i.e., dealbreaker answers), let me know.