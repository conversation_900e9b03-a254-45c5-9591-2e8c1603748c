import { db } from '../db';
import { sql } from 'drizzle-orm';
import logger from '../utils/logger';
import MessagingService from './messaging-service';
import { cacheService } from './redis-cache';

export interface Lead {
  id: number;
  dealershipId: number;
  conversationId?: number;
  source: string;
  status: 'new' | 'contacted' | 'qualified' | 'unqualified' | 'converted' | 'lost';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  
  // Customer information
  customerName?: string;
  customerEmail?: string;
  customerPhone?: string;
  
  // Lead details
  interestedVehicle?: string;
  priceRange?: string;
  timeframe?: string;
  tradein?: boolean;
  financing?: boolean;
  
  // Scoring
  leadScore: number;
  scoreFactors: Record<string, number>;
  
  // Tracking
  firstContactAt?: Date;
  lastContactAt?: Date;
  nextFollowUpAt?: Date;
  followUpCount: number;
  responseCount: number;
  
  // Metadata
  notes?: string;
  assignedTo?: number; // User ID
  tags: string[];
  customFields: Record<string, any>;
  
  createdAt: Date;
  updatedAt: Date;
}

export interface LeadScoringRule {
  id: string;
  dealershipId: number;
  name: string;
  conditions: ScoringCondition[];
  score: number;
  active: boolean;
}

export interface ScoringCondition {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than' | 'in_range';
  value: any;
  weight: number;
}

export interface LeadSource {
  id: string;
  dealershipId: number;
  name: string;
  type: 'website' | 'phone' | 'email' | 'chat' | 'social' | 'referral' | 'other';
  active: boolean;
  baseScore: number;
  followUpRuleId?: string;
}

export class LeadManagementService {
  private messagingService: MessagingService;

  constructor() {
    this.messagingService = new MessagingService();
  }

  // Create a new lead from conversation
  async createLead(params: {
    dealershipId: number;
    conversationId?: number;
    source: string;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    initialData?: Record<string, any>;
  }): Promise<number> {
    try {
      const leadId = await this.generateLeadId();
      
      // Get source configuration
      const leadSource = await this.getLeadSource(params.dealershipId, params.source);
      const baseScore = leadSource?.baseScore || 50;

      // Calculate initial lead score
      const leadScore = await this.calculateLeadScore({
        dealershipId: params.dealershipId,
        source: params.source,
        customerEmail: params.customerEmail,
        customerPhone: params.customerPhone,
        ...params.initialData
      });

      const lead: Omit<Lead, 'id'> = {
        dealershipId: params.dealershipId,
        conversationId: params.conversationId,
        source: params.source,
        status: 'new',
        priority: this.determinePriority(leadScore),
        customerName: params.customerName,
        customerEmail: params.customerEmail,
        customerPhone: params.customerPhone,
        leadScore: Math.max(baseScore, leadScore),
        scoreFactors: { base: baseScore, calculated: leadScore },
        followUpCount: 0,
        responseCount: 0,
        tags: [],
        customFields: params.initialData || {},
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await this.saveLead(leadId, lead);

      // Setup follow-up sequence if configured
      if (leadSource?.followUpRuleId) {
        await this.messagingService.setupFollowUpSequence(
          leadId,
          params.conversationId || 0,
          params.dealershipId,
          params.source,
          params.customerEmail,
          params.customerPhone
        );
      }

      // Invalidate relevant caches
      await this.invalidateLeadCaches(params.dealershipId);

      logger.info('Lead created', {
        leadId,
        dealershipId: params.dealershipId,
        source: params.source,
        score: lead.leadScore
      });

      return leadId;
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error creating lead', { error: err.message, params });
      throw err;
    }
  }

  // Update lead information
  async updateLead(leadId: number, updates: Partial<Lead>): Promise<void> {
    try {
      const currentLead = await this.getLead(leadId);
      if (!currentLead) {
        throw new Error(`Lead not found: ${leadId}`);
      }

      // Recalculate score if relevant fields changed
      let newScore = currentLead.leadScore;
      if (this.shouldRecalculateScore(updates)) {
        newScore = await this.calculateLeadScore({
          ...currentLead,
          ...updates
        });
      }

      const updatedLead = {
        ...currentLead,
        ...updates,
        leadScore: newScore,
        priority: this.determinePriority(newScore),
        updatedAt: new Date()
      };

      await this.saveLead(leadId, updatedLead);
      await this.invalidateLeadCaches(currentLead.dealershipId);

      logger.info('Lead updated', { leadId, updates: Object.keys(updates) });
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error updating lead', { error: err.message, leadId });
      throw err;
    }
  }

  // Record customer interaction
  async recordInteraction(leadId: number, type: 'response' | 'contact', metadata?: any): Promise<void> {
    try {
      const lead = await this.getLead(leadId);
      if (!lead) return;

      const updates: Partial<Lead> = {
        lastContactAt: new Date()
      };

      if (type === 'response') {
        updates.responseCount = lead.responseCount + 1;
        
        // Increase score for engagement
        const engagementBonus = Math.min(lead.responseCount * 5, 20);
        updates.leadScore = lead.leadScore + engagementBonus;
        updates.scoreFactors = {
          ...lead.scoreFactors,
          engagement: engagementBonus
        };
      } else if (type === 'contact') {
        updates.followUpCount = lead.followUpCount + 1;
        
        if (!lead.firstContactAt) {
          updates.firstContactAt = new Date();
        }
      }

      await this.updateLead(leadId, updates);

      // Log the interaction
      await this.logInteraction(leadId, type, metadata);

      logger.info('Interaction recorded', { leadId, type });
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error recording interaction', { error: err.message, leadId, type });
    }
  }

  // Get leads with filtering and pagination
  async getLeads(params: {
    dealershipId: number;
    page?: number;
    limit?: number;
    status?: string;
    source?: string;
    priority?: string;
    assignedTo?: number;
    searchTerm?: string;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
  }): Promise<{ leads: Lead[]; total: number; page: number; totalPages: number }> {
    try {
      const page = params.page || 1;
      const limit = params.limit || 20;
      const offset = (page - 1) * limit;

      // Build cache key
      const cacheKey = `leads:${params.dealershipId}:${JSON.stringify(params)}`;
      
      return await cacheService.getOrSet(cacheKey, async () => {
        // Build WHERE conditions
        const conditions = [`dealership_id = ${params.dealershipId}`];
        
        if (params.status) conditions.push(`status = '${params.status}'`);
        if (params.source) conditions.push(`source = '${params.source}'`);
        if (params.priority) conditions.push(`priority = '${params.priority}'`);
        if (params.assignedTo) conditions.push(`assigned_to = ${params.assignedTo}`);
        
        if (params.searchTerm) {
          const searchPattern = `%${params.searchTerm}%`;
          conditions.push(`(
            customer_name ILIKE '${searchPattern}' OR 
            customer_email ILIKE '${searchPattern}' OR 
            customer_phone ILIKE '${searchPattern}'
          )`);
        }

        const whereClause = conditions.join(' AND ');
        const sortBy = params.sortBy || 'created_at';
        const sortOrder = params.sortOrder || 'desc';

        // Get leads
        const leadsQuery = sql.raw(`
          SELECT * FROM leads 
          WHERE ${whereClause}
          ORDER BY ${sortBy} ${sortOrder}
          LIMIT ${limit} OFFSET ${offset}
        `);

        // Get total count
        const countQuery = sql.raw(`
          SELECT COUNT(*) as total FROM leads 
          WHERE ${whereClause}
        `);

        const [leadsResult, countResult] = await Promise.all([
          db.execute(leadsQuery),
          db.execute(countQuery)
        ]);

        const leads = leadsResult.rows.map(row => this.mapRowToLead(row));
        const total = parseInt((countResult.rows[0] as any).total);

        return {
          leads,
          total,
          page,
          totalPages: Math.ceil(total / limit)
        };
      }, { ttl: 60 }); // Cache for 1 minute

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error getting leads', { error: err.message, params });
      throw err;
    }
  }

  // Get single lead
  async getLead(leadId: number): Promise<Lead | null> {
    try {
      const cacheKey = `lead:${leadId}`;
      
      return await cacheService.getOrSet(cacheKey, async () => {
        const result = await db.execute(sql`
          SELECT * FROM leads WHERE id = ${leadId}
        `);

        if (result.rows.length === 0) return null;
        return this.mapRowToLead(result.rows[0]);
      }, { ttl: 300 }); // Cache for 5 minutes

    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      logger.error('Error getting lead', { error: err.message, leadId });
      return null;
    }
  }

  // Lead scoring methods
  async calculateLeadScore(leadData: any): Promise<number> {
    try {
      const rules = await this.getScoringRules(leadData.dealershipId);
      let totalScore = 0;

      for (const rule of rules) {
        if (!rule.active) continue;

        const ruleApplies = rule.conditions.every(condition => 
          this.evaluateCondition(condition, leadData)
        );

        if (ruleApplies) {
          totalScore += rule.score;
        }
      }

      // Ensure score is within bounds
      return Math.max(0, Math.min(100, totalScore));
    } catch (error) {
      logger.error('Error calculating lead score', { error, leadData });
      return 50; // Default score
    }
  }

  private evaluateCondition(condition: ScoringCondition, data: any): boolean {
    const fieldValue = data[condition.field];
    
    switch (condition.operator) {
      case 'equals':
        return fieldValue === condition.value;
      case 'contains':
        return typeof fieldValue === 'string' && 
               fieldValue.toLowerCase().includes(condition.value.toLowerCase());
      case 'greater_than':
        return Number(fieldValue) > Number(condition.value);
      case 'less_than':
        return Number(fieldValue) < Number(condition.value);
      case 'in_range':
        const num = Number(fieldValue);
        return num >= condition.value.min && num <= condition.value.max;
      default:
        return false;
    }
  }

  private determinePriority(score: number): 'low' | 'medium' | 'high' | 'urgent' {
    if (score >= 90) return 'urgent';
    if (score >= 70) return 'high';
    if (score >= 50) return 'medium';
    return 'low';
  }

  private shouldRecalculateScore(updates: Partial<Lead>): boolean {
    const scoringFields = [
      'customerEmail', 'customerPhone', 'interestedVehicle', 
      'priceRange', 'timeframe', 'tradein', 'financing'
    ];
    
    return scoringFields.some(field => field in updates);
  }

  // Database operations
  private async saveLead(leadId: number, lead: Omit<Lead, 'id'>): Promise<void> {
    try {
      await db.execute(sql`
        INSERT INTO leads (
          id, dealership_id, conversation_id, source, status, priority,
          customer_name, customer_email, customer_phone,
          interested_vehicle, price_range, timeframe, tradein, financing,
          lead_score, score_factors, first_contact_at, last_contact_at,
          next_follow_up_at, follow_up_count, response_count,
          notes, assigned_to, tags, custom_fields, created_at, updated_at
        ) VALUES (
          ${leadId}, ${lead.dealershipId}, ${lead.conversationId},
          ${lead.source}, ${lead.status}, ${lead.priority},
          ${lead.customerName}, ${lead.customerEmail}, ${lead.customerPhone},
          ${lead.interestedVehicle}, ${lead.priceRange}, ${lead.timeframe},
          ${lead.tradein}, ${lead.financing}, ${lead.leadScore},
          ${JSON.stringify(lead.scoreFactors)}, ${lead.firstContactAt?.toISOString()},
          ${lead.lastContactAt?.toISOString()}, ${lead.nextFollowUpAt?.toISOString()},
          ${lead.followUpCount}, ${lead.responseCount}, ${lead.notes},
          ${lead.assignedTo}, ${JSON.stringify(lead.tags)},
          ${JSON.stringify(lead.customFields)}, ${lead.createdAt.toISOString()},
          ${lead.updatedAt.toISOString()}
        )
        ON CONFLICT (id) DO UPDATE SET
          status = EXCLUDED.status,
          priority = EXCLUDED.priority,
          customer_name = EXCLUDED.customer_name,
          customer_email = EXCLUDED.customer_email,
          customer_phone = EXCLUDED.customer_phone,
          interested_vehicle = EXCLUDED.interested_vehicle,
          price_range = EXCLUDED.price_range,
          timeframe = EXCLUDED.timeframe,
          tradein = EXCLUDED.tradein,
          financing = EXCLUDED.financing,
          lead_score = EXCLUDED.lead_score,
          score_factors = EXCLUDED.score_factors,
          first_contact_at = EXCLUDED.first_contact_at,
          last_contact_at = EXCLUDED.last_contact_at,
          next_follow_up_at = EXCLUDED.next_follow_up_at,
          follow_up_count = EXCLUDED.follow_up_count,
          response_count = EXCLUDED.response_count,
          notes = EXCLUDED.notes,
          assigned_to = EXCLUDED.assigned_to,
          tags = EXCLUDED.tags,
          custom_fields = EXCLUDED.custom_fields,
          updated_at = EXCLUDED.updated_at
      `);
    } catch (error) {
      logger.error('Error saving lead', { error, leadId });
      throw error;
    }
  }

  private mapRowToLead(row: any): Lead {
    return {
      id: row.id,
      dealershipId: row.dealership_id,
      conversationId: row.conversation_id,
      source: row.source,
      status: row.status,
      priority: row.priority,
      customerName: row.customer_name,
      customerEmail: row.customer_email,
      customerPhone: row.customer_phone,
      interestedVehicle: row.interested_vehicle,
      priceRange: row.price_range,
      timeframe: row.timeframe,
      tradein: row.tradein,
      financing: row.financing,
      leadScore: row.lead_score,
      scoreFactors: JSON.parse(row.score_factors || '{}'),
      firstContactAt: row.first_contact_at ? new Date(row.first_contact_at) : undefined,
      lastContactAt: row.last_contact_at ? new Date(row.last_contact_at) : undefined,
      nextFollowUpAt: row.next_follow_up_at ? new Date(row.next_follow_up_at) : undefined,
      followUpCount: row.follow_up_count,
      responseCount: row.response_count,
      notes: row.notes,
      assignedTo: row.assigned_to,
      tags: JSON.parse(row.tags || '[]'),
      customFields: JSON.parse(row.custom_fields || '{}'),
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  // Helper methods
  private async generateLeadId(): Promise<number> {
    // Simple increment - you might want to use a sequence or UUID
    return Date.now() + Math.floor(Math.random() * 1000);
  }

  private async getScoringRules(dealershipId: number): Promise<LeadScoringRule[]> {
    try {
      const result = await db.execute(sql`
        SELECT * FROM lead_scoring_rules 
        WHERE dealership_id = ${dealershipId} AND active = true
      `);

      return result.rows.map((row: any) => ({
        id: row.id,
        dealershipId: row.dealership_id,
        name: row.name,
        conditions: JSON.parse(row.conditions || '[]'),