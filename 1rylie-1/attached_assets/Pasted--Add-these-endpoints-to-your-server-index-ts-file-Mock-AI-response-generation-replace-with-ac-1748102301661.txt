// Add these endpoints to your server/index.ts file

// Mock AI response generation (replace with actual AI service)
const generateMockAIResponse = (prompt: string, scenario?: string) => {
  const responses = [
    "Hey John! I totally get wanting to know the value of your truck before deciding on a trade. It sounds like you've taken great care of it! While I can't give you an exact number here, you can use our online tool to get a rough idea. Want me to send you that link?",
    "Thanks for reaching out! I'd be happy to help you explore your options. Based on what you've shared, it sounds like you're looking for something reliable and efficient. Let me show you a few vehicles that might be perfect for your needs.",
    "I appreciate you considering us for your next vehicle! Given your timeline and preferences, I have some great options to share with you. Would you prefer to discuss financing options first, or would you like to see what we have in stock?",
    "That's a great question! Safety is definitely a priority, especially with family considerations. Our latest models come with advanced safety features that have earned top ratings. Would you like me to walk you through the specific safety technologies?",
    "I understand you want to make sure you're getting the best deal. We currently have some excellent incentives running that could save you significant money. Plus, our certified pre-owned program offers great value with warranty coverage."
  ];
  return responses[Math.floor(Math.random() * responses.length)];
};

// Mock handover dossier generation
const generateMockHandoverDossier = (conversationHistory: string) => {
  return {
    customerName: "<PERSON>",
    customerContact: "(*************",
    conversationSummary: "John is interested in trading in his current truck (120k miles, well-maintained) and is looking for a rough value assessment. He's unsure about timing but wants to explore options without committing to an in-person visit yet.",
    customerInsights: [
      { key: "Trade-in Interest", value: "High interest in trade-in value assessment", confidence: 0.95 },
      { key: "Purchase Timeline", value: "Considering but not urgent", confidence: 0.8 },
      { key: "Information Gathering", value: "Wants remote valuation tools", confidence: 0.9 }
    ],
    vehicleInterests: [
      { make: "Ford", model: "F-150", year: 2024, confidence: 0.7 },
      { make: "Chevrolet", model: "Silverado", year: 2024, confidence: 0.6 }
    ],
    suggestedApproach: "Focus on providing the online valuation tool first to build trust. Follow up with inventory options once they have their trade value. Emphasize no-pressure approach and convenience.",
    urgency: "medium" as const,
    escalationReason: "Customer wants human assistance for trade valuation",
    responseSuggestions: [
      {
        text: "I can send you our online trade-in tool right now - it takes about 2 minutes and gives you a real estimate.",
        context: "When customer asks about trade-in value",
        category: "trade_in"
      },
      {
        text: "Once you see what your truck is worth, I'd love to show you what that value can get you in our current inventory.",
        context: "After providing valuation tool",
        category: "follow_up"
      }
    ]
  };
};

// Prompt testing endpoint
app.post('/api/prompt-test', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { prompt, variables, customerScenario, testType } = req.body;
    
    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    // Process the prompt template with variables
    let processedPrompt = prompt;
    if (variables && typeof variables === 'object') {
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`\\{\\{\\s*${key}\\s*\\}\\}`, 'g');
        processedPrompt = processedPrompt.replace(regex, String(value));
      });
    }

    let response;
    
    if (testType === 'handover') {
      // Generate handover dossier
      const handoverDossier = generateMockHandoverDossier(customerScenario || '');
      response = {
        success: true,
        testType: 'handover',
        processedPrompt,
        handoverDossier,
        timestamp: new Date().toISOString()
      };
    } else {
      // Generate AI response
      const aiResponse = generateMockAIResponse(processedPrompt, customerScenario);
      response = {
        success: true,
        testType: 'chat',
        processedPrompt,
        aiResponse,
        customerScenario: customerScenario || null,
        analysis: {
          customerName: "John Smith",
          query: customerScenario || "General inquiry",
          analysis: "Customer is in information-gathering phase, showing interest but not ready to commit.",
          insights: "Looking for reassurance and value assessment before proceeding.",
          channel: "chat",
          salesReadiness: "medium",
          handoverNeeded: customerScenario?.toLowerCase().includes('trade') || customerScenario?.toLowerCase().includes('price') ? true : false
        },
        timestamp: new Date().toISOString()
      };
    }

    // Store the test result in database (optional)
    try {
      const pool = await getPool();
      await pool.query(
        'INSERT INTO prompt_tests (user_id, original_prompt, processed_prompt, ai_response, variables, test_type, customer_scenario, created_at) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW())',
        [
          req.session.userId, 
          prompt, 
          processedPrompt, 
          JSON.stringify(response), 
          JSON.stringify(variables || {}),
          testType || 'chat',
          customerScenario || null
        ]
      );
    } catch (dbError) {
      console.warn('Could not store test result:', dbError);
      // Continue without storing - don't fail the request
    }

    res.json(response);

  } catch (error) {
    console.error('Prompt test error:', error);
    res.status(500).json({ 
      error: 'Failed to test prompt',
      message: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// Get prompt templates (library)
app.get('/api/prompt-templates', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    // Mock prompt templates (replace with database query)
    const templates = [
      {
        id: 1,
        name: "Automotive Sales Agent",
        description: "Professional automotive sales agent for customer interactions",
        template: "You are an automotive sales agent named {{Agent_Name}}, working for {{Dealership}}. Rewrite if off: Trim wordiness, boost empathy, fix compliance, or adjust tone to sound like a real salesperson (e.g., replace \"We strive to assist\" with \"We've got you covered!\").",
        variables: ["Agent_Name", "Dealership"],
        category: "sales",
        isActive: true
      },
      {
        id: 2,
        name: "Lead Qualification",
        description: "Qualify leads and gather customer requirements",
        template: "You are a lead qualification specialist for {{Company}}. Your goal is to understand the customer's needs for {{Vehicle_Type}} and budget range of {{Budget_Range}}. Ask qualifying questions and provide relevant options.",
        variables: ["Company", "Vehicle_Type", "Budget_Range"],
        category: "qualification",
        isActive: true
      },
      {
        id: 3,
        name: "Trade-in Specialist",
        description: "Handle trade-in inquiries and valuations",
        template: "You are a trade-in specialist at {{Dealership}}. Help customers understand the trade-in process for their {{Current_Vehicle}}. Provide guidance on valuation and next steps while building confidence in our appraisal process.",
        variables: ["Dealership", "Current_Vehicle"],
        category: "trade-in",
        isActive: true
      },
      {
        id: 4,
        name: "Service Follow-up",
        description: "Follow up with customers after service appointments",
        template: "You are following up with {{Customer_Name}} regarding their recent {{Service_Type}} service at {{Dealership}}. Thank them for their business and ensure their satisfaction with the service provided.",
        variables: ["Customer_Name", "Service_Type", "Dealership"],
        category: "service",
        isActive: true
      }
    ];

    res.json({ templates });

  } catch (error) {
    console.error('Error fetching prompt templates:', error);
    res.status(500).json({ error: 'Failed to fetch prompt templates' });
  }
});

// Save/update prompt template
app.post('/api/prompt-templates', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    const { name, description, template, variables, category } = req.body;
    
    if (!name || !template) {
      return res.status(400).json({ error: 'Name and template are required' });
    }

    // Mock save (replace with actual database insert)
    const newTemplate = {
      id: Date.now(), // Mock ID
      name,
      description: description || '',
      template,
      variables: variables || [],
      category: category || 'general',
      isActive: true,
      createdBy: req.session.userId,
      createdAt: new Date().toISOString()
    };

    res.json({ 
      success: true, 
      template: newTemplate,
      message: 'Template saved successfully'
    });

  } catch (error) {
    console.error('Error saving prompt template:', error);
    res.status(500).json({ error: 'Failed to save prompt template' });
  }
});

// Get test history
app.get('/api/prompt-tests', async (req, res) => {
  try {
    if (!req.session.userId) {
      return res.status(401).json({ error: 'Authentication required' });
    }

    try {
      const pool = await getPool();
      const result = await pool.query(
        'SELECT id, original_prompt, processed_prompt, ai_response, variables, test_type, customer_scenario, created_at FROM prompt_tests WHERE user_id = $1 ORDER BY created_at DESC LIMIT 50',
        [req.session.userId]
      );

      res.json({
        tests: result.rows
      });
    } catch (dbError) {
      console.warn('Database query failed, returning empty history:', dbError);
      res.json({ tests: [] });
    }

  } catch (error) {
    console.error('Error fetching prompt tests:', error);
    res.status(500).json({ error: 'Failed to fetch prompt tests' });
  }
});