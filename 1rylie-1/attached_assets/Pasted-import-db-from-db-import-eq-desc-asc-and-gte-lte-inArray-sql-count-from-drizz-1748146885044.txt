import { db } from '../db';
import { eq, desc, asc, and, gte, lte, inArray, sql, count } from 'drizzle-orm';
import { conversations, messages, users, dealerships, leadScores, followUps } from '../../shared/schema';
import { logger } from '../logger';

// ===================================
// OPTIMIZED CONVERSATION QUERIES
// ===================================

/**
 * Get recent conversations for a dealership with optimized pagination
 */
export async function getRecentConversations(
  dealershipId: number,
  page: number = 1,
  limit: number = 20,
  status?: string
) {
  const offset = (page - 1) * limit;
  
  try {
    // Use the optimized index: idx_conversations_dealership_created
    let query = db
      .select({
        id: conversations.id,
        customerId: conversations.customer_id,
        status: conversations.status,
        createdAt: conversations.created_at,
        updatedAt: conversations.updated_at,
        messageCount: sql<number>`(
          SELECT COUNT(*) FROM messages 
          WHERE conversation_id = ${conversations.id}
        )`
      })
      .from(conversations)
      .where(eq(conversations.dealership_id, dealershipId))
      .orderBy(desc(conversations.created_at))
      .limit(limit)
      .offset(offset);

    if (status) {
      query = query.where(
        and(
          eq(conversations.dealership_id, dealershipId),
          eq(conversations.status, status)
        )
      );
    }

    // Get total count for pagination (optimized with same index)
    const totalQuery = db
      .select({ count: count() })
      .from(conversations)
      .where(
        status 
          ? and(
              eq(conversations.dealership_id, dealershipId),
              eq(conversations.status, status)
            )
          : eq(conversations.dealership_id, dealershipId)
      );

    const [results, totalResult] = await Promise.all([
      query,
      totalQuery
    ]);

    return {
      conversations: results,
      total: totalResult[0].count,
      page,
      limit,
      totalPages: Math.ceil(totalResult[0].count / limit)
    };
  } catch (error) {
    logger.error('Error fetching conversations:', error);
    throw error;
  }
}

/**
 * Get conversation with messages (optimized for chat display)
 */
export async function getConversationWithMessages(
  conversationId: number,
  messageLimit: number = 50
) {
  try {
    // Use optimized indexes for both queries
    const [conversation, conversationMessages] = await Promise.all([
      // Get conversation details
      db.select()
        .from(conversations)
        .where(eq(conversations.id, conversationId))
        .limit(1),
      
      // Get recent messages (uses idx_messages_conversation_timestamp)
      db.select({
        id: messages.id,
        content: messages.content,
        senderType: messages.sender_type,
        senderName: messages.sender_name,
        createdAt: messages.created_at,
        metadata: messages.metadata
      })
        .from(messages)
        .where(eq(messages.conversation_id, conversationId))
        .orderBy(desc(messages.created_at))
        .limit(messageLimit)
    ]);

    if (!conversation[0]) {
      return null;
    }

    return {
      ...conversation[0],
      messages: conversationMessages.reverse() // Show oldest first for chat
    };
  } catch (error) {
    logger.error('Error fetching conversation with messages:', error);
    throw error;
  }
}

/**
 * Get active conversations count by dealership (real-time dashboard)
 */
export async function getActiveConversationsCount(dealershipId: number) {
  try {
    // Uses idx_conversations_status_dealership
    const result = await db
      .select({ 
        active: count(),
        status: conversations.status 
      })
      .from(conversations)
      .where(
        and(
          eq(conversations.dealership_id, dealershipId),
          inArray(conversations.status, ['active', 'pending', 'waiting'])
        )
      )
      .groupBy(conversations.status);

    return result.reduce((acc, curr) => {
      acc[curr.status] = curr.active;
      return acc;
    }, {} as Record<string, number>);
  } catch (error) {
    logger.error('Error fetching active conversations count:', error);
    throw error;
  }
}

// ===================================
// OPTIMIZED LEAD SCORING QUERIES
// ===================================

/**
 * Get top leads for a dealership (optimized for sales dashboard)
 */
export async function getTopLeads(
  dealershipId: number, 
  limit: number = 10,
  minScore: number = 0.7
) {
  try {
    // Uses idx_lead_scores_dealership_top
    const topLeads = await db
      .select({
        conversationId: leadScores.conversation_id,
        score: leadScores.score,
        calculatedAt: leadScores.calculated_at,
        // Join conversation details
        customerId: conversations.customer_id,
        conversationStatus: conversations.status,
        conversationCreated: conversations.created_at,
        // Calculate recency boost
        recencyScore: sql<number>`
          CASE 
            WHEN ${leadScores.calculated_at} > NOW() - INTERVAL '1 day' THEN ${leadScores.score} * 1.2
            WHEN ${leadScores.calculated_at} > NOW() - INTERVAL '3 days' THEN ${leadScores.score} * 1.1
            ELSE ${leadScores.score}
          END
        `
      })
      .from(leadScores)
      .innerJoin(conversations, eq(leadScores.conversation_id, conversations.id))
      .where(
        and(
          eq(leadScores.dealership_id, dealershipId),
          gte(leadScores.score, minScore)
        )
      )
      .orderBy(desc(sql`recency_score`), desc(leadScores.calculated_at))
      .limit(limit);

    return topLeads;
  } catch (error) {
    logger.error('Error fetching top leads:', error);
    throw error;
  }
}

/**
 * Batch update lead scores (optimized for background processing)
 */
export async function batchUpdateLeadScores(
  scores: Array<{
    conversationId: number;
    dealershipId: number;
    score: number;
    factors: Record<string, any>;
  }>
) {
  try {
    // Use batch insert/update for better performance
    const values = scores.map(s => ({
      conversation_id: s.conversationId,
      dealership_id: s.dealershipId,
      score: s.score,
      factors: s.factors,
      calculated_at: new Date()
    }));

    // Use ON CONFLICT for upsert behavior
    await db
      .insert(leadScores)
      .values(values)
      .onConflictDoUpdate({
        target: [leadScores.conversation_id],
        set: {
          score: sql`EXCLUDED.score`,
          factors: sql`EXCLUDED.factors`,
          calculated_at: sql`EXCLUDED.calculated_at`
        }
      });

    logger.info(`Updated ${scores.length} lead scores`);
  } catch (error) {
    logger.error('Error batch updating lead scores:', error);
    throw error;
  }
}

// ===================================
// OPTIMIZED USER QUERIES
// ===================================

/**
 * Fast user authentication lookup
 */
export async function findUserForAuth(loginIdentifier: string) {
  try {
    // Uses idx_users_email_active or idx_users_username_active
    const user = await db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        password: users.password,
        name: users.name,
        role: users.role,
        dealership_id: users.dealership_id,
        is_verified: users.is_verified,
        is_active: users.is_active,
        last_login: users.last_login
      })
      .from(users)
      .where(
        and(
          sql`(${users.email} = ${loginIdentifier} OR ${users.username} = ${loginIdentifier})`,
          eq(users.is_active, true)
        )
      )
      .limit(1);

    return user[0] || null;
  } catch (error) {
    logger.error('Error finding user for auth:', error);
    throw error;
  }
}

/**
 * Get dealership users with roles (optimized for admin dashboard)
 */
export async function getDealershipUsers(
  dealershipId: number,
  includeInactive: boolean = false
) {
  try {
    // Uses idx_users_dealership_role
    let query = db
      .select({
        id: users.id,
        username: users.username,
        email: users.email,
        name: users.name,
        role: users.role,
        is_verified: users.is_verified,
        is_active: users.is_active,
        last_login: users.last_login,
        created_at: users.created_at
      })
      .from(users)
      .where(eq(users.dealership_id, dealershipId))
      .orderBy(asc(users.role), asc(users.name));

    if (!includeInactive) {
      query = query.where(
        and(
          eq(users.dealership_id, dealershipId),
          eq(users.is_active, true)
        )
      );
    }

    return await query;
  } catch (error) {
    logger.error('Error fetching dealership users:', error);
    throw error;
  }
}

// ===================================
// OPTIMIZED FOLLOW-UP QUERIES
// ===================================

/**
 * Get user's upcoming follow-ups (dashboard widget)
 */
export async function getUserUpcomingFollowUps(
  userId: number,
  days: number = 7
) {
  try {
    // Uses idx_follow_ups_assigned_user
    const followUps = await db
      .select({
        id: followUps.id,
        conversationId: followUps.conversation_id,
        customerName: followUps.customer_name,
        customerContact: followUps.customer_contact,
        scheduledTime: followUps.scheduled_time,
        notes: followUps.notes,
        status: followUps.status,
        // Join conversation for context
        conversationStatus: conversations.status
      })
      .from(followUps)
      .leftJoin(conversations, eq(followUps.conversation_id, conversations.id))
      .where(
        and(
          eq(followUps.assigned_to, userId),
          inArray(followUps.status, ['pending', 'scheduled']),
          lte(followUps.scheduled_time, sql`NOW() + INTERVAL '${days} days'`),
          gte(followUps.scheduled_time, sql`NOW()`)
        )
      )
      .orderBy(asc(followUps.scheduled_time))
      .limit(20);

    return followUps;
  } catch (error) {
    logger.error('Error fetching user follow-ups:', error);
    throw error;
  }
}

/**
 * Get overdue follow-ups for processing
 */
export async function getOverdueFollowUps(limit: number = 100) {
  try {
    // Uses idx_follow_ups_overdue
    const overdueFollowUps = await db
      .select({
        id: followUps.id,
        assignedTo: followUps.assigned_to,
        dealershipId: followUps.dealership_id,
        customerName: followUps.customer_name,
        scheduledTime: followUps.scheduled_time,
        // Calculate how overdue
        hoursOverdue: sql<number>`
          EXTRACT(EPOCH FROM (NOW() - ${followUps.scheduled_time})) / 3600
        `
      })
      .from(followUps)
      .where(
        and(
          eq(followUps.status, 'pending'),
          sql`${followUps.scheduled_time} < NOW()`
        )
      )
      .orderBy(asc(followUps.scheduled_time))
      .limit(limit);

    return overdueFollowUps;
  } catch (error) {
    logger.error('Error fetching overdue follow-ups:', error);
    throw error;
  }
}

// ===================================
// ANALYTICS AND REPORTING QUERIES
// ===================================

/**
 * Get conversation analytics for dealership dashboard
 */
export async function getConversationAnalytics(
  dealershipId: number,
  startDate: Date,
  endDate: Date
) {
  try {
    const analytics = await db
      .select({
        date: sql<string>`DATE(${conversations.created_at})`,
        totalConversations: count(),
        activeConversations: sql<number>`
          COUNT(*) FILTER (WHERE ${conversations.status} = 'active')
        `,
        completedConversations: sql<number>`
          COUNT(*) FILTER (WHERE ${conversations.status} = 'completed')
        `,
        avgMessagesPerConversation: sql<number>`
          AVG((
            SELECT COUNT(*) FROM ${messages} 
            WHERE conversation_id = ${conversations.id}
          ))
        `
      })
      .from(conversations)
      .where(
        and(
          eq(conversations.dealership_id, dealershipId),
          gte(conversations.created_at, startDate),
          lte(conversations.created_at, endDate)
        )
      )
      .groupBy(sql`DATE(${conversations.created_at})`)
      .orderBy(sql`DATE(${conversations.created_at})`);

    return analytics;
  } catch (error) {
    logger.error('Error fetching conversation analytics:', error);
    throw error;
  }
}

// Export all optimized functions
export {
  getRecentConversations,
  getConversationWithMessages,
  getActiveConversationsCount,
  getTopLeads,
  batchUpdateLeadScores,
  findUserForAuth,
  getDealershipUsers,
  getUserUpcomingFollowUps,
  getOverdueFollowUps,
  getConversationAnalytics
};