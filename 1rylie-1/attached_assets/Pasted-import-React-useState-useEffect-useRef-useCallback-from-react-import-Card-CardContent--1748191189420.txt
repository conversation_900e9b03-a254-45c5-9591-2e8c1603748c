import React, { useState, useEffect, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Bot, 
  User, 
  Settings, 
  TestTube, 
  Send,
  Phone,
  Mail,
  Clock,
  Circle,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';

interface ChatMessage {
  id: string;
  conversationId: number;
  senderId: number;
  senderType: 'agent' | 'customer';
  content: string;
  messageType: 'text' | 'image' | 'file';
  timestamp: Date;
  metadata?: any;
}

interface WebSocketMessage {
  type: string;
  [key: string]: any;
}

const ChatTestPage: React.FC = () => {
  // Test configuration state
  const [mode, setMode] = useState<'rylie_ai' | 'direct_agent'>('direct_agent');
  const [userType, setUserType] = useState<'agent' | 'customer'>('customer');
  const [dealershipId] = useState(1);
  const [conversationId] = useState(1);

  // WebSocket state
  const [isConnected, setIsConnected] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionId, setConnectionId] = useState<string | null>(null);
  const [lastActivity, setLastActivity] = useState<Date | null>(null);
  
  // Chat state
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [isTyping, setIsTyping] = useState(false);
  const [typingUsers, setTypingUsers] = useState<Set<number>>(new Set());
  
  const wsRef = useRef<WebSocket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 5;
  const reconnectInterval = 3000;

  const customerInfo = {
    name: 'John Doe',
    email: '<EMAIL>',
    phone: '+****************'
  };

  // WebSocket connection management
  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setIsConnecting(true);
    
    try {
      const wsUrl = `ws://localhost:5000/ws/chat`;
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
        setIsConnecting(false);
        reconnectAttempts.current = 0;
        setLastActivity(new Date());
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);
        setIsConnecting(false);
        setConnectionId(null);

        // Attempt to reconnect if not a clean close
        if (event.code !== 1000 && reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++;
          console.log(`Attempting to reconnect... (${reconnectAttempts.current}/${maxReconnectAttempts})`);
          
          setTimeout(() => {
            connect();
          }, reconnectInterval * Math.pow(2, reconnectAttempts.current - 1));
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnecting(false);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          setLastActivity(new Date());
          handleIncomingMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      setIsConnecting(false);
    }
  }, []);

  const sendMessage = useCallback((message: WebSocketMessage) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      wsRef.current.send(JSON.stringify(message));
      setLastActivity(new Date());
    } else {
      console.warn('WebSocket is not connected. Message not sent:', message);
    }
  }, []);

  const handleIncomingMessage = (data: WebSocketMessage) => {
    switch (data.type) {
      case 'connection_established':
        setConnectionId(data.connectionId);
        console.log('Connection established with ID:', data.connectionId);
        break;

      case 'authenticated':
        console.log('User authenticated:', data.userType);
        break;

      case 'joined_conversation':
        console.log('Joined conversation:', data.conversationId);
        if (data.recentMessages && Array.isArray(data.recentMessages)) {
          setMessages(data.recentMessages.map((msg: any) => ({
            ...msg,
            timestamp: new Date(msg.timestamp)
          })));
        }
        break;

      case 'new_message':
        if (data.message) {
          setMessages(prev => [...prev, {
            ...data.message,
            timestamp: new Date(data.message.timestamp)
          }]);
        }
        break;

      case 'typing_indicator':
        if (data.isTyping && data.userId !== (userType === 'customer' ? 999 : 1)) {
          setTypingUsers(prev => new Set([...prev, data.userId]));
        } else {
          setTypingUsers(prev => {
            const newSet = new Set(prev);
            newSet.delete(data.userId);
            return newSet;
          });
        }
        break;

      case 'user_joined':
        console.log('User joined:', data.userId, data.userType);
        break;

      case 'user_left':
        console.log('User left:', data.userId, data.userType);
        break;

      case 'error':
        console.error('WebSocket error:', data.error);
        break;

      case 'pong':
        // Handle ping/pong for connection health
        break;

      default:
        console.log('Unknown message type:', data.type, data);
    }
  };

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Connect on mount
  useEffect(() => {
    connect();

    return () => {
      if (wsRef.current) {
        wsRef.current.close(1000, 'Component unmounted');
      }
    };
  }, [connect]);

  // Authenticate and join conversation when connected
  useEffect(() => {
    if (isConnected && connectionId) {
      // Authenticate
      sendMessage({
        type: 'authenticate',
        token: 'demo-token',
        userId: userType === 'customer' ? 999 : 1,
        dealershipId,
        userType
      });

      // Join conversation
      setTimeout(() => {
        sendMessage({
          type: 'join_conversation',
          conversationId
        });
      }, 100);
    }
  }, [isConnected, connectionId, dealershipId, conversationId, userType, sendMessage]);

  // Send periodic pings to keep connection alive
  useEffect(() => {
    if (!isConnected) return;

    const pingInterval = setInterval(() => {
      sendMessage({ type: 'ping' });
    }, 30000);

    return () => clearInterval(pingInterval);
  }, [isConnected, sendMessage]);

  const handleSendMessage = () => {
    if (!message.trim() || !isConnected) return;

    const messageData = {
      type: 'send_message',
      content: message.trim(),
      messageType: 'text',
      metadata: {
        customerInfo,
        timestamp: new Date().toISOString()
      }
    };

    sendMessage(messageData);
    setMessage('');

    // Stop typing indicator
    if (isTyping) {
      sendMessage({
        type: 'typing',
        isTyping: false
      });
      setIsTyping(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleTyping = (value: string) => {
    setMessage(value);

    // Send typing indicator
    if (value.trim() && !isTyping) {
      setIsTyping(true);
      sendMessage({
        type: 'typing',
        isTyping: true
      });
    } else if (!value.trim() && isTyping) {
      setIsTyping(false);
      sendMessage({
        type: 'typing',
        isTyping: false
      });
    }
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const getConnectionStatus = () => {
    if (isConnecting) return { icon: Loader2, text: 'Connecting...', color: 'yellow' };
    if (isConnected) return { icon: CheckCircle, text: 'Connected', color: 'green' };
    return { icon: AlertCircle, text: 'Disconnected', color: 'red' };
  };

  const status = getConnectionStatus();
  const StatusIcon = status.icon;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <TestTube className="w-8 h-8 text-blue-500" />
          Rylie Chat System Test
        </h1>
        <p className="text-gray-600">
          Test the dual-mode chat system with WebSocket integration
        </p>
      </div>

      {/* Configuration Panel */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="w-5 h-5" />
            Test Configuration
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">Operation Mode</label>
              <Select value={mode} onValueChange={(value: 'rylie_ai' | 'direct_agent') => setMode(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rylie_ai">
                    <div className="flex items-center gap-2">
                      <Bot className="w-4 h-4 text-blue-500" />
                      Rylie AI Mode
                    </div>
                  </SelectItem>
                  <SelectItem value="direct_agent">
                    <div className="flex items-center gap-2">
                      <User className="w-4 h-4 text-green-500" />
                      Direct Agent Mode
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">User Type</label>
              <Select value={userType} onValueChange={(value: 'agent' | 'customer') => setUserType(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="customer">Customer View</SelectItem>
                  <SelectItem value="agent">Agent View</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Badge variant="outline">Dealership ID: {dealershipId}</Badge>
            <Badge variant="outline">Conversation ID: {conversationId}</Badge>
            <Badge variant={mode === 'rylie_ai' ? 'default' : 'secondary'}>
              {mode === 'rylie_ai' ? 'AI Mode' : 'Agent Mode'}
            </Badge>
            <Badge variant="outline">{userType === 'agent' ? 'Agent' : 'Customer'}</Badge>
          </div>
        </CardContent>
      </Card>

      {/* Chat Interface */}
      <Card className="flex flex-col h-[600px] max-w-2xl mx-auto">
        {/* Header */}
        <CardHeader className="flex-shrink-0 border-b">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              {mode === 'rylie_ai' ? (
                <>
                  <Bot className="w-5 h-5 text-blue-500" />
                  <span>Rylie AI Assistant</span>
                </>
              ) : (
                <>
                  <User className="w-5 h-5 text-green-500" />
                  <span>Live Chat Support</span>
                </>
              )}
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <StatusIcon className={`w-4 h-4 ${
                status.color === 'green' ? 'text-green-500' : 
                status.color === 'yellow' ? 'text-yellow-500 animate-spin' : 
                'text-red-500'
              }`} />
              <span className="text-sm text-gray-600">{status.text}</span>
            </div>
          </div>
          
          {/* Customer Info (for agents) */}
          {userType === 'agent' && (
            <div className="flex items-center gap-4 text-sm text-gray-600 mt-2">
              <div className="flex items-center gap-1">
                <User className="w-3 h-3" />
                {customerInfo.name}
              </div>
              <div className="flex items-center gap-1">
                <Mail className="w-3 h-3" />
                {customerInfo.email}
              </div>
              <div className="flex items-center gap-1">
                <Phone className="w-3 h-3" />
                {customerInfo.phone}
              </div>
            </div>
          )}
        </CardHeader>

        {/* Messages */}
        <CardContent className="flex-1 p-0">
          <ScrollArea className="h-full p-4">
            <div className="space-y-4">
              {messages.length === 0 && (
                <div className="text-center text-gray-500 py-8">
                  <div className="mb-2">
                    {mode === 'rylie_ai' ? (
                      <Bot className="w-8 h-8 mx-auto text-blue-400" />
                    ) : (
                      <User className="w-8 h-8 mx-auto text-green-400" />
                    )}
                  </div>
                  <p>
                    {mode === 'rylie_ai' 
                      ? 'Hi! I\'m Rylie, your AI assistant. How can I help you today?'
                      : 'Welcome! An agent will be with you shortly.'
                    }
                  </p>
                </div>
              )}

              {messages.map((msg) => {
                const isOwn = userType === 'agent' ? msg.senderType === 'agent' : msg.senderType === 'customer';
                const isBot = msg.senderType === 'agent' && mode === 'rylie_ai';
                
                return (
                  <div
                    key={msg.id}
                    className={`flex ${isOwn ? 'justify-end' : 'justify-start'} gap-2`}
                  >
                    {!isOwn && (
                      <Avatar className="w-8 h-8">
                        <AvatarFallback>
                          {isBot ? (
                            <Bot className="w-4 h-4 text-blue-500" />
                          ) : (
                            <User className="w-4 h-4 text-green-500" />
                          )}
                        </AvatarFallback>
                      </Avatar>
                    )}
                    
                    <div className={`max-w-[70%] ${isOwn ? 'order-first' : ''}`}>
                      <div
                        className={`rounded-lg px-3 py-2 ${
                          isOwn
                            ? 'bg-blue-500 text-white'
                            : isBot
                            ? 'bg-blue-50 border border-blue-200'
                            : 'bg-gray-100'
                        }`}
                      >
                        <p className="text-sm">{msg.content}</p>
                      </div>
                      <div className={`text-xs text-gray-500 mt-1 ${isOwn ? 'text-right' : 'text-left'}`}>
                        <span className="flex items-center gap-1">
                          <Clock className="w-3 h-3" />
                          {formatTime(msg.timestamp)}
                        </span>
                      </div>
                    </div>
                    
                    {isOwn && (
                      <Avatar className="w-8 h-8">
                        <AvatarFallback>
                          <User className="w-4 h-4" />
                        </AvatarFallback>
                      </Avatar>
                    )}
                  </div>
                );
              })}

              {/* Typing Indicator */}
              {typingUsers.size > 0 && (
                <div className="flex justify-start gap-2">
                  <Avatar className="w-8 h-8">
                    <AvatarFallback>
                      {mode === 'rylie_ai' ? (
                        <Bot className="w-4 h-4 text-blue-500" />
                      ) : (
                        <User className="w-4 h-4 text-green-500" />
                      )}
                    </AvatarFallback>
                  </Avatar>
                  <div className="bg-gray-100 rounded-lg px-3 py-2">
                    <div className="flex space-x-1">
                      <Circle className="w-2 h-2 animate-bounce text-gray-400" />
                      <Circle className="w-2 h-2 animate-bounce text-gray-400" style={{ animationDelay: '0.1s' }} />
                      <Circle className="w-2 h-2 animate-bounce text-gray-400" style={{ animationDelay: '0.2s' }} />
                    </div>
                  </div>
                </div>
              )}
              
              <div ref={messagesEndRef} />
            </div>
          </ScrollArea>
        </CardContent>

        {/* Input */}
        <div className="flex-shrink-0 border-t p-4">
          <div className="flex gap-2">
            <Input
              value={message}
              onChange={(e) => handleTyping(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder={
                isConnected 
                  ? "Type your message..." 
                  : "Connecting..."
              }
              disabled={!isConnected}
              className="flex-1"
            />
            <Button
              onClick={handleSendMessage}
              disabled={!message.trim() || !isConnected}
              size="sm"
            >
              <Send className="w-4 h-4" />
            </Button>
          </div>
          
          {lastActivity && (
            <div className="text-xs text-gray-500 mt-2">
              Last activity: {lastActivity.toLocaleTimeString()}
            </div>
          )}
        </div>
      </Card>

      {/* Test Instructions */}
      <Card className="max-w-2xl mx-auto">
        <CardHeader>
          <CardTitle>Test Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3 text-sm">
          <div className="space-y-2">
            <h4 className="font-medium">WebSocket Connection Test:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li>Check connection status indicator (green = connected)</li>
              <li>Open browser dev tools to see WebSocket messages</li>
              <li>Send a message and verify it appears in the chat</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">Mode Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li><strong>Rylie AI Mode:</strong> Should show AI assistant branding</li>
              <li><strong>Direct Agent Mode:</strong> Should show live chat support branding</li>
              <li>Switch between modes to test different UI states</li>
            </ul>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium">User Type Testing:</h4>
            <ul className="list-disc list-inside space-y-1 text-gray-600">
              <li><strong>Customer View:</strong> Standard chat interface</li>
              <li><strong>Agent View:</strong> Shows customer info header</li>
            </ul>
          </div>

          <div className="p-3 bg-blue-50 rounded-lg">
            <p className="text-blue-800 text-xs">
              <strong>Note:</strong> Make sure your server is running with WebSocket support. 
              Check the browser console for any connection errors.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ChatTestPage;