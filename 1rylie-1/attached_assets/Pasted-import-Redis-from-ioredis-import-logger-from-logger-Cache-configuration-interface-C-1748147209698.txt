import Redis from 'ioredis';
import { logger } from '../logger';

// Cache configuration
interface CacheConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  keyPrefix: string;
  retryDelayOnFailover: number;
  maxRetriesPerRequest: number;
  enableOfflineQueue: boolean;
}

interface CacheOptions {
  ttl?: number; // Time to live in seconds
  compress?: boolean;
  version?: string; // For cache invalidation
}

class CacheService {
  private redis: Redis | null = null;
  private isConnected = false;
  private fallbackCache = new Map<string, { data: any; expires: number }>();
  private readonly config: CacheConfig;

  // Cache TTL constants (in seconds)
  private readonly TTL = {
    SHORT: 5 * 60,        // 5 minutes - frequently changing data
    MEDIUM: 30 * 60,      // 30 minutes - moderate frequency
    LONG: 2 * 60 * 60,    // 2 hours - stable data
    VERY_LONG: 24 * 60 * 60, // 24 hours - rarely changing data
    SESSION: 60 * 60      // 1 hour - session data
  };

  constructor() {
    this.config = {
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      password: process.env.REDIS_PASSWORD,
      db: parseInt(process.env.REDIS_DB || '0'),
      keyPrefix: process.env.REDIS_KEY_PREFIX || 'rylie:',
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      enableOfflineQueue: false
    };
  }

  async initialize(): Promise<void> {
    if (process.env.SKIP_REDIS === 'true') {
      logger.info('Redis caching disabled, using in-memory fallback');
      return;
    }

    try {
      this.redis = new Redis({
        ...this.config,
        lazyConnect: true,
        maxRetriesPerRequest: this.config.maxRetriesPerRequest,
        retryDelayOnFailover: this.config.retryDelayOnFailover,
        enableOfflineQueue: this.config.enableOfflineQueue,
        
        // Connection event handlers
        retryConnect: (times) => {
          const delay = Math.min(times * 50, 2000);
          logger.warn(`Redis reconnecting in ${delay}ms (attempt ${times})`);
          return delay;
        }
      });

      // Event handlers
      this.redis.on('connect', () => {
        logger.info('Redis connected successfully');
        this.isConnected = true;
      });

      this.redis.on('error', (error) => {
        logger.error('Redis connection error:', error);
        this.isConnected = false;
      });

      this.redis.on('close', () => {
        logger.warn('Redis connection closed');
        this.isConnected = false;
      });

      // Test connection
      await this.redis.connect();
      await this.redis.ping();
      
      logger.info('Redis cache service initialized');
    } catch (error) {
      logger.error('Failed to initialize Redis:', error);
      logger.info('Falling back to in-memory cache');
      this.redis = null;
    }
  }

  private buildKey(key: string, version?: string): string {
    const versionSuffix = version ? `:v${version}` : '';
    return `${this.config.keyPrefix}${key}${versionSuffix}`;
  }

  private async setInMemory(key: string, data: any, ttl: number): Promise<void> {
    const expires = Date.now() + (ttl * 1000);
    this.fallbackCache.set(key, { data, expires });
    
    // Cleanup expired entries periodically
    if (this.fallbackCache.size > 1000) {
      this.cleanupInMemoryCache();
    }
  }

  private getFromMemory(key: string): any | null {
    const cached = this.fallbackCache.get(key);
    if (!cached) return null;
    
    if (Date.now() > cached.expires) {
      this.fallbackCache.delete(key);
      return null;
    }
    
    return cached.data;
  }

  private cleanupInMemoryCache(): void {
    const now = Date.now();
    for (const [key, value] of this.fallbackCache.entries()) {
      if (now > value.expires) {
        this.fallbackCache.delete(key);
      }
    }
  }

  async set(key: string, data: any, options: CacheOptions = {}): Promise<boolean> {
    const { ttl = this.TTL.MEDIUM, version } = options;
    const fullKey = this.buildKey(key, version);

    try {
      const serializedData = JSON.stringify(data);
      
      if (this.redis && this.isConnected) {
        await this.redis.setex(fullKey, ttl, serializedData);
        return true;
      } else {
        this.setInMemory(fullKey, data, ttl);
        return true;
      }
    } catch (error) {
      logger.error(`Cache set error for key ${key}:`, error);
      return false;
    }
  }

  async get<T = any>(key: string, options: CacheOptions = {}): Promise<T | null> {
    const { version } = options;
    const fullKey = this.buildKey(key, version);

    try {
      let serializedData: string | null = null;
      
      if (this.redis && this.isConnected) {
        serializedData = await this.redis.get(fullKey);
      } else {
        const memoryData = this.getFromMemory(fullKey);
        if (memoryData !== null) {
          return memoryData as T;
        }
      }

      if (serializedData === null) {
        return null;
      }

      return JSON.parse(serializedData) as T;
    } catch (error) {
      logger.error(`Cache get error for key ${key}:`, error);
      return null;
    }
  }

  async del(key: string, version?: string): Promise<boolean> {
    const fullKey = this.buildKey(key, version);

    try {
      if (this.redis && this.isConnected) {
        await this.redis.del(fullKey);
      } else {
        this.fallbackCache.delete(fullKey);
      }
      return true;
    } catch (error) {
      logger.error(`Cache delete error for key ${key}:`, error);
      return false;
    }
  }

  async flush(): Promise<boolean> {
    try {
      if (this.redis && this.isConnected) {
        await this.redis.flushdb();
      } else {
        this.fallbackCache.clear();
      }
      logger.info('Cache flushed successfully');
      return true;
    } catch (error) {
      logger.error('Cache flush error:', error);
      return false;
    }
  }

  // Specialized caching methods for common use cases

  // ===================================
  // DEALERSHIP DATA CACHING
  // ===================================

  async cacheDealershipSettings(dealershipId: number, settings: any): Promise<void> {
    await this.set(
      `dealership:${dealershipId}:settings`, 
      settings, 
      { ttl: this.TTL.LONG }
    );
  }

  async getDealershipSettings(dealershipId: number): Promise<any | null> {
    return this.get(`dealership:${dealershipId}:settings`);
  }

  async cacheDealershipBranding(dealershipId: number, branding: any): Promise<void> {
    await this.set(
      `dealership:${dealershipId}:branding`, 
      branding, 
      { ttl: this.TTL.LONG }
    );
  }

  async getDealershipBranding(dealershipId: number): Promise<any | null> {
    return this.get(`dealership:${dealershipId}:branding`);
  }

  // ===================================
  // USER SESSION CACHING
  // ===================================

  async cacheUserSession(userId: number, sessionData: any): Promise<void> {
    await this.set(
      `user:${userId}:session`, 
      sessionData, 
      { ttl: this.TTL.SESSION }
    );
  }

  async getUserSession(userId: number): Promise<any | null> {
    return this.get(`user:${userId}:session`);
  }

  async invalidateUserSession(userId: number): Promise<void> {
    await this.del(`user:${userId}:session`);
  }

  // ===================================
  // CONVERSATION DATA CACHING
  // ===================================

  async cacheConversation(conversationId: number, conversation: any): Promise<void> {
    await this.set(
      `conversation:${conversationId}`, 
      conversation, 
      { ttl: this.TTL.SHORT }
    );
  }

  async getConversation(conversationId: number): Promise<any | null> {
    return this.get(`conversation:${conversationId}`);
  }

  async cacheConversationMessages(conversationId: number, messages: any[]): Promise<void> {
    await this.set(
      `conversation:${conversationId}:messages`, 
      messages, 
      { ttl: this.TTL.SHORT }
    );
  }

  async getConversationMessages(conversationId: number): Promise<any[] | null> {
    return this.get(`conversation:${conversationId}:messages`);
  }

  async invalidateConversationCache(conversationId: number): Promise<void> {
    await Promise.all([
      this.del(`conversation:${conversationId}`),
      this.del(`conversation:${conversationId}:messages`)
    ]);
  }

  // ===================================
  // LEAD SCORING CACHE
  // ===================================

  async cacheLeadScores(dealershipId: number, leads: any[]): Promise<void> {
    await this.set(
      `dealership:${dealershipId}:top-leads`, 
      leads, 
      { ttl: this.TTL.MEDIUM }
    );
  }

  async getLeadScores(dealershipId: number): Promise<any[] | null> {
    return this.get(`dealership:${dealershipId}:top-leads`);
  }

  async cacheConversationLeadScore(conversationId: number, score: any): Promise<void> {
    await this.set(
      `conversation:${conversationId}:lead-score`, 
      score, 
      { ttl: this.TTL.LONG }
    );
  }

  async getConversationLeadScore(conversationId: number): Promise<any | null> {
    return this.get(`conversation:${conversationId}:lead-score`);
  }

  // ===================================
  // ANALYTICS CACHING
  // ===================================

  async cacheDealershipAnalytics(
    dealershipId: number, 
    startDate: string, 
    endDate: string, 
    analytics: any
  ): Promise<void> {
    const key = `dealership:${dealershipId}:analytics:${startDate}:${endDate}`;
    await this.set(key, analytics, { ttl: this.TTL.MEDIUM });
  }

  async getDealershipAnalytics(
    dealershipId: number, 
    startDate: string, 
    endDate: string
  ): Promise<any | null> {
    const key = `dealership:${dealershipId}:analytics:${startDate}:${endDate}`;
    return this.get(key);
  }

  // ===================================
  // CACHE WARMING AND MANAGEMENT
  // ===================================

  async warmDealershipCache(dealershipId: number): Promise<void> {
    try {
      logger.info(`Warming cache for dealership ${dealershipId}`);
      
      // Import optimized queries (avoid circular dependency)
      const { 
        getTopLeads, 
        getActiveConversationsCount 
      } = await import('./optimized-queries');

      // Warm frequently accessed data
      const [topLeads, activeCounts] = await Promise.all([
        getTopLeads(dealershipId, 10),
        getActiveConversationsCount(dealershipId)
      ]);

      await Promise.all([
        this.cacheLeadScores(dealershipId, topLeads),
        this.set(`dealership:${dealershipId}:active-counts`, activeCounts, { ttl: this.TTL.SHORT })
      ]);

      logger.info(`Cache warmed for dealership ${dealershipId}`);
    } catch (error) {
      logger.error(`Failed to warm cache for dealership ${dealershipId}:`, error);
    }
  }

  async invalidateDealershipCache(dealershipId: number): Promise<void> {
    try {
      const pattern = `dealership:${dealershipId}:*`;
      
      if (this.redis && this.isConnected) {
        const keys = await this.redis.keys(this.buildKey(pattern));
        if (keys.length > 0) {
          await this.redis.del(...keys);
        }
      } else {
        // In-memory cache cleanup
        for (const key of this.fallbackCache.keys()) {
          if (key.includes(`dealership:${dealershipId}:`)) {
            this.fallbackCache.delete(key);
          }
        }
      }
      
      logger.info(`Invalidated cache for dealership ${dealershipId}`);
    } catch (error) {
      logger.error(`Failed to invalidate dealership cache:`, error);
    }
  }

  // ===================================
  // CACHE STATISTICS AND MONITORING
  // ===================================

  async getCacheStats(): Promise<{
    connected: boolean;
    memoryUsage?: string;
    keyCount?: number;
    hitRate?: number;
    fallbackCacheSize: number;
  }> {
    const stats = {
      connected: this.isConnected,
      fallbackCacheSize: this.fallbackCache.size
    };

    if (this.redis && this.isConnected) {
      try {
        const info = await this.redis.info('memory');
        const keyCount = await this.redis.dbsize();
        
        // Parse memory usage from info string
        const memoryMatch = info.match(/used_memory_human:(.+)/);
        const memoryUsage = memoryMatch ? memoryMatch[1].trim() : 'Unknown';

        return {
          ...stats,
          memoryUsage,
          keyCount
        };
      } catch (error) {
        logger.error('Error getting cache stats:', error);
      }
    }

    return stats;
  }

  async healthCheck(): Promise<{ status: 'healthy' | 'degraded' | 'unhealthy'; details: string }> {
    if (this.redis && this.isConnected) {
      try {
        const start = Date.now();
        await this.redis.ping();
        const latency = Date.now() - start;
        
        if (latency < 10) {
          return { status: 'healthy', details: `Redis responding in ${latency}ms` };
        } else if (latency < 100) {
          return { status: 'degraded', details: `Redis slow response: ${latency}ms` };
        } else {
          return { status: 'unhealthy', details: `Redis very slow: ${latency}ms` };
        }
      } catch (error) {
        return { status: 'unhealthy', details: `Redis error: ${error.message}` };
      }
    } else {
      return { 
        status: 'degraded', 
        details: `Using in-memory fallback (${this.fallbackCache.size} items)` 
      };
    }
  }

  async close(): Promise<void> {
    if (this.redis) {
      try {
        await this.redis.quit();
        logger.info('Redis connection closed');
      } catch (error) {
        logger.error('Error closing Redis connection:', error);
      }
    }
    this.fallbackCache.clear();
  }
}

// ===================================
// CACHE MIDDLEWARE AND HELPERS
// ===================================

/**
 * Cache middleware for Express routes
 */
export function cacheMiddleware(
  keyGenerator: (req: any) => string,
  ttl: number = 300,
  options: { skipIf?: (req: any) => boolean } = {}
) {
  return async (req: any, res: any, next: any) => {
    try {
      // Skip caching if condition met
      if (options.skipIf && options.skipIf(req)) {
        return next();
      }

      const cacheKey = keyGenerator(req);
      const cached = await cacheService.get(cacheKey);

      if (cached) {
        logger.debug(`Cache hit for key: ${cacheKey}`);
        res.setHeader('X-Cache', 'HIT');
        return res.json(cached);
      }

      // Store original json function
      const originalJson = res.json;

      // Override json function to cache response
      res.json = function(data: any) {
        // Cache successful responses only
        if (res.statusCode >= 200 && res.statusCode < 300) {
          cacheService.set(cacheKey, data, { ttl }).catch(error => {
            logger.error('Failed to cache response:', error);
          });
        }
        
        res.setHeader('X-Cache', 'MISS');
        return originalJson.call(this, data);
      };

      next();
    } catch (error) {
      logger.error('Cache middleware error:', error);
      next();
    }
  };
}

/**
 * Cache-aside pattern helper
 */
export async function cacheAside<T>(
  key: string,
  fetchFunction: () => Promise<T>,
  options: CacheOptions = {}
): Promise<T> {
  // Try to get from cache first
  const cached = await cacheService.get<T>(key, options);
  if (cached !== null) {
    return cached;
  }

  // Fetch from source
  const data = await fetchFunction();
  
  // Cache the result
  await cacheService.set(key, data, options);
  
  return data;
}

/**
 * Batch cache operations
 */
export async function batchCacheSet(
  items: Array<{ key: string; data: any; options?: CacheOptions }>
): Promise<void> {
  await Promise.all(
    items.map(item => 
      cacheService.set(item.key, item.data, item.options)
    )
  );
}

// Export singleton instance
export const cacheService = new CacheService();

// Convenience exports
export { CacheService };
export default cacheService;