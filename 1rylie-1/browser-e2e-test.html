<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rylie <PERSON> - Browser E2E Testing</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .content {
            padding: 30px;
        }
        
        .test-section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            overflow: hidden;
        }
        
        .test-section h3 {
            background: #f8f9fa;
            padding: 15px 20px;
            margin: 0;
            border-bottom: 1px solid #e0e0e0;
            color: #2c3e50;
        }
        
        .test-content {
            padding: 20px;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .test-button {
            background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
            position: relative;
        }
        
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
        }
        
        .test-button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
        }
        
        .test-button.success {
            background: linear-gradient(135deg, #27ae60 0%, #229954 100%);
        }
        
        .test-button.error {
            background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        }
        
        .test-button.warning {
            background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
        }
        
        .results {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .result-item {
            padding: 8px 0;
            border-bottom: 1px solid #e0e0e0;
            font-family: 'Courier New', monospace;
            font-size: 13px;
        }
        
        .result-item:last-child {
            border-bottom: none;
        }
        
        .result-item.success {
            color: #27ae60;
        }
        
        .result-item.error {
            color: #e74c3c;
        }
        
        .result-item.warning {
            color: #f39c12;
        }
        
        .summary {
            background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
        }
        
        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .stat {
            text-align: center;
            padding: 15px;
            background: rgba(255,255,255,0.1);
            border-radius: 6px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
            margin-left: 10px;
        }
        
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
            margin: 15px 0;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2980b9);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 Rylie AI E2E Testing</h1>
            <p>Comprehensive Browser-Based Testing Suite</p>
        </div>
        
        <div class="content">
            <!-- Frontend Page Testing -->
            <div class="test-section">
                <h3>🖥️ Frontend Page Testing</h3>
                <div class="test-content">
                    <div class="button-grid" id="pageTests">
                        <!-- Buttons will be generated by JavaScript -->
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="pageProgress"></div>
                    </div>
                </div>
            </div>
            
            <!-- API Endpoint Testing -->
            <div class="test-section">
                <h3>🔌 API Endpoint Testing</h3>
                <div class="test-content">
                    <div class="button-grid" id="apiTests">
                        <!-- Buttons will be generated by JavaScript -->
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="apiProgress"></div>
                    </div>
                </div>
            </div>
            
            <!-- Interactive Feature Testing -->
            <div class="test-section">
                <h3>⚡ Interactive Feature Testing</h3>
                <div class="test-content">
                    <div class="button-grid">
                        <button class="test-button" onclick="testConversationFlow()">
                            Test Conversation Flow
                        </button>
                        <button class="test-button" onclick="testFormSubmissions()">
                            Test Form Submissions
                        </button>
                        <button class="test-button" onclick="testButtonClicks()">
                            Test Button Interactions
                        </button>
                        <button class="test-button" onclick="testNavigationFlow()">
                            Test Navigation Flow
                        </button>
                        <button class="test-button" onclick="testErrorHandling()">
                            Test Error Scenarios
                        </button>
                        <button class="test-button" onclick="testPerformance()">
                            Test Performance
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Control Panel -->
            <div class="test-section">
                <h3>🎛️ Control Panel</h3>
                <div class="test-content">
                    <div class="button-grid">
                        <button class="test-button" onclick="runAllTests()" style="background: linear-gradient(135deg, #8e44ad 0%, #9b59b6 100%);">
                            🚀 Run All Tests
                        </button>
                        <button class="test-button" onclick="clearResults()">
                            🗑️ Clear Results
                        </button>
                        <button class="test-button" onclick="exportResults()">
                            📄 Export Results
                        </button>
                        <button class="test-button" onclick="openDevTools()">
                            🔧 Open Dev Tools
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Results Display -->
            <div class="results" id="results">
                <div class="result-item">Ready to start testing... Click any test button above!</div>
            </div>
            
            <!-- Summary -->
            <div class="summary" id="summary" style="display: none;">
                <h3>📊 Test Summary</h3>
                <div class="summary-stats">
                    <div class="stat">
                        <div class="stat-number" id="totalTests">0</div>
                        <div>Total Tests</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="passedTests">0</div>
                        <div>Passed</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="failedTests">0</div>
                        <div>Failed</div>
                    </div>
                    <div class="stat">
                        <div class="stat-number" id="successRate">0%</div>
                        <div>Success Rate</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        const BASE_URL = window.location.origin;
        const testResults = [];
        
        // Pages to test
        const pages = [
            { name: 'Home', url: '/' },
            { name: 'Auth', url: '/auth' },
            { name: 'Login', url: '/login' },
            { name: 'Prompt Testing', url: '/prompt-testing' },
            { name: 'Enhanced Prompt Testing', url: '/enhanced-prompt-testing' },
            { name: 'Prompt Library', url: '/prompt-library' },
            { name: 'System', url: '/system' },
            { name: 'Admin Dealerships', url: '/admin/dealerships' },
            { name: 'Analytics', url: '/analytics' },
            { name: 'Security', url: '/security' },
            { name: 'Setup', url: '/setup' }
        ];
        
        // API endpoints to test
        const apiEndpoints = [
            { name: 'Health Check', url: '/health', method: 'GET' },
            { name: 'API Docs', url: '/api/docs', method: 'GET' },
            { name: 'OpenAPI Spec', url: '/api/docs/openapi.json', method: 'GET' },
            { name: 'Conversations', url: '/api/conversations', method: 'GET' },
            { name: 'Dealerships', url: '/api/dealerships', method: 'GET' },
            { name: 'Users', url: '/api/users', method: 'GET' },
            { name: 'System Health', url: '/api/monitoring/health', method: 'GET' },
            { name: 'System Metrics', url: '/api/monitoring/metrics', method: 'GET' }
        ];
        
        // Initialize the testing interface
        function initializeTests() {
            generatePageTestButtons();
            generateAPITestButtons();
            logResult('🚀 Testing interface initialized', 'success');
        }
        
        // Generate page test buttons
        function generatePageTestButtons() {
            const container = document.getElementById('pageTests');
            pages.forEach(page => {
                const button = document.createElement('button');
                button.className = 'test-button';
                button.textContent = `Test ${page.name}`;
                button.onclick = () => testPage(page.name, page.url, button);
                container.appendChild(button);
            });
        }
        
        // Generate API test buttons
        function generateAPITestButtons() {
            const container = document.getElementById('apiTests');
            apiEndpoints.forEach(endpoint => {
                const button = document.createElement('button');
                button.className = 'test-button';
                button.textContent = `Test ${endpoint.name}`;
                button.onclick = () => testAPI(endpoint.name, endpoint.url, endpoint.method, button);
                container.appendChild(button);
            });
        }
        
        // Test a page
        async function testPage(name, url, button) {
            button.disabled = true;
            button.innerHTML = `Testing ${name}... <div class="loading"></div>`;
            
            try {
                const startTime = Date.now();
                const response = await fetch(BASE_URL + url);
                const duration = Date.now() - startTime;
                
                if (response.ok) {
                    button.className = 'test-button success';
                    button.innerHTML = `✅ ${name}`;
                    logResult(`✅ Page ${name}: PASS (${response.status}, ${duration}ms)`, 'success');
                    testResults.push({ name: `Page ${name}`, status: 'PASS', duration });
                } else {
                    button.className = 'test-button error';
                    button.innerHTML = `❌ ${name}`;
                    logResult(`❌ Page ${name}: FAIL (${response.status}, ${duration}ms)`, 'error');
                    testResults.push({ name: `Page ${name}`, status: 'FAIL', duration });
                }
            } catch (error) {
                button.className = 'test-button error';
                button.innerHTML = `❌ ${name}`;
                logResult(`❌ Page ${name}: ERROR (${error.message})`, 'error');
                testResults.push({ name: `Page ${name}`, status: 'ERROR', error: error.message });
            }
            
            button.disabled = false;
            updateSummary();
        }
        
        // Test an API endpoint
        async function testAPI(name, url, method, button) {
            button.disabled = true;
            button.innerHTML = `Testing ${name}... <div class="loading"></div>`;
            
            try {
                const startTime = Date.now();
                const response = await fetch(BASE_URL + url, { method });
                const duration = Date.now() - startTime;
                
                if (response.ok) {
                    button.className = 'test-button success';
                    button.innerHTML = `✅ ${name}`;
                    logResult(`✅ API ${name}: PASS (${response.status}, ${duration}ms)`, 'success');
                    testResults.push({ name: `API ${name}`, status: 'PASS', duration });
                } else {
                    button.className = 'test-button warning';
                    button.innerHTML = `⚠️ ${name}`;
                    logResult(`⚠️ API ${name}: WARN (${response.status}, ${duration}ms)`, 'warning');
                    testResults.push({ name: `API ${name}`, status: 'WARN', duration });
                }
            } catch (error) {
                button.className = 'test-button error';
                button.innerHTML = `❌ ${name}`;
                logResult(`❌ API ${name}: ERROR (${error.message})`, 'error');
                testResults.push({ name: `API ${name}`, status: 'ERROR', error: error.message });
            }
            
            button.disabled = false;
            updateSummary();
        }
        
        // Log a result
        function logResult(message, type = 'info') {
            const results = document.getElementById('results');
            const item = document.createElement('div');
            item.className = `result-item ${type}`;
            item.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            results.appendChild(item);
            results.scrollTop = results.scrollHeight;
        }
        
        // Update summary
        function updateSummary() {
            const summary = document.getElementById('summary');
            const total = testResults.length;
            const passed = testResults.filter(r => r.status === 'PASS').length;
            const failed = testResults.filter(r => r.status === 'FAIL' || r.status === 'ERROR').length;
            const successRate = total > 0 ? Math.round((passed / total) * 100) : 0;
            
            document.getElementById('totalTests').textContent = total;
            document.getElementById('passedTests').textContent = passed;
            document.getElementById('failedTests').textContent = failed;
            document.getElementById('successRate').textContent = `${successRate}%`;
            
            if (total > 0) {
                summary.style.display = 'block';
            }
        }
        
        // Clear results
        function clearResults() {
            testResults.length = 0;
            document.getElementById('results').innerHTML = '<div class="result-item">Results cleared. Ready for new tests!</div>';
            document.getElementById('summary').style.display = 'none';
            
            // Reset all buttons
            document.querySelectorAll('.test-button').forEach(button => {
                button.className = 'test-button';
                button.disabled = false;
                const originalText = button.textContent.replace(/[✅❌⚠️]/g, '').trim();
                button.textContent = originalText;
            });
        }
        
        // Run all tests
        async function runAllTests() {
            clearResults();
            logResult('🚀 Starting comprehensive test suite...', 'success');
            
            // Test all pages
            for (const page of pages) {
                const button = document.querySelector(`button[onclick*="${page.name}"]`);
                if (button) {
                    await testPage(page.name, page.url, button);
                    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
                }
            }
            
            // Test all APIs
            for (const endpoint of apiEndpoints) {
                const button = document.querySelector(`button[onclick*="${endpoint.name}"]`);
                if (button) {
                    await testAPI(endpoint.name, endpoint.url, endpoint.method, button);
                    await new Promise(resolve => setTimeout(resolve, 500)); // Small delay
                }
            }
            
            logResult('🎉 All tests completed!', 'success');
        }
        
        // Export results
        function exportResults() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    total: testResults.length,
                    passed: testResults.filter(r => r.status === 'PASS').length,
                    failed: testResults.filter(r => r.status === 'FAIL' || r.status === 'ERROR').length
                },
                results: testResults
            };
            
            const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `rylie-e2e-test-report-${new Date().toISOString().split('T')[0]}.json`;
            a.click();
            URL.revokeObjectURL(url);
            
            logResult('📄 Test report exported', 'success');
        }
        
        // Open dev tools
        function openDevTools() {
            logResult('💡 Press F12 or Ctrl+Shift+I to open developer tools', 'info');
        }
        
        // Placeholder functions for interactive tests
        function testConversationFlow() {
            logResult('🗨️ Testing conversation flow...', 'info');
            // Add conversation flow testing logic here
        }
        
        function testFormSubmissions() {
            logResult('📝 Testing form submissions...', 'info');
            // Add form testing logic here
        }
        
        function testButtonClicks() {
            logResult('🖱️ Testing button interactions...', 'info');
            // Add button testing logic here
        }
        
        function testNavigationFlow() {
            logResult('🧭 Testing navigation flow...', 'info');
            // Add navigation testing logic here
        }
        
        function testErrorHandling() {
            logResult('🚫 Testing error scenarios...', 'info');
            // Add error testing logic here
        }
        
        function testPerformance() {
            logResult('⚡ Testing performance...', 'info');
            // Add performance testing logic here
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initializeTests);
    </script>
</body>
</html>
