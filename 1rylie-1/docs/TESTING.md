# Testing Guide

This document outlines how to test the Rylie AI platform to ensure all components are functioning correctly.

## Platform Testing

### Basic Platform Test

To verify that the platform is properly set up and running, use the included test script:

```bash
node test-platform.js
```

This script checks:
- Server connectivity (on port 5000)
- Database connection
- API endpoints

### Test Environment Setup

For testing, the platform uses a separate database named "rylie_test" to avoid affecting production data. Configure this in your `.env` file:

```
DATABASE_URL=postgresql://username:password@hostname:port/rylie_test
```

## Component Testing

### Database Testing

To test database connectivity:

```bash
# Using the PostgreSQL client
psql -U postgres -d rylie_test -c "SELECT 'Database connection successful' AS status;"

# Using the application test script
npm run test:db
```

### API Testing

To test API endpoints:

```bash
# Health check endpoint
curl http://localhost:5000/api/health

# Status endpoint
curl http://localhost:5000/api/status
```

### Integration Testing

Run the integration test suite:

```bash
npm run test:integration
```

## Troubleshooting Test Issues

### Common Test Failures

| Issue | Solution |
|-------|----------|
| Server connectivity failure | Ensure server is running on port 5000 |
| Database connection failure | Verify DATABASE_URL and that PostgreSQL is running |
| "Database does not exist" error | Run `createdb rylie_test` to create the test database |
| API endpoint failures | Check server logs for specific errors |

### PostgreSQL Client Installation

If the PostgreSQL client (psql) is not installed:

**macOS:**
```bash
brew install postgresql
```

**Ubuntu/Debian:**
```bash
sudo apt-get install postgresql-client
```

**Windows:**
Download and install from the [PostgreSQL website](https://www.postgresql.org/download/windows/)

## Continuous Integration

The platform includes Jest tests that can be run in a CI environment:

```bash
npm test
```

This will run all unit and integration tests with coverage reporting.