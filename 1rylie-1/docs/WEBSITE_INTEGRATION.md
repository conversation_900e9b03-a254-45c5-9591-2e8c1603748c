
# Rylie AI Chat Widget Installation Guide

This guide explains how to integrate the Rylie AI chat widget into your website.

## Quick Installation

Add the following script tag just before the closing `</body>` tag of your website:

```html
<script src="https://your-repl-domain.repl.co/embed.js" data-auto-init="true"></script>
```

That's it! The chat widget will automatically appear in the bottom-right corner of your website.

## Installation Options

### 1. Auto-Initialize (Recommended)
```html
<script src="https://your-repl-domain.repl.co/embed.js" data-auto-init="true"></script>
```

### 2. Manual Initialization
```html
<script src="https://your-repl-domain.repl.co/embed.js"></script>
<script>
  RylieChat.init();
</script>
```

### 3. Container-Based
```html
<div id="rylie-chat"></div>
<script src="https://your-repl-domain.repl.co/embed.js"></script>
```

## Chat Widget Features

- Automatically appears in bottom-right corner
- Responsive design (mobile-friendly)
- Real-time conversation capabilities
- Customizable appearance

## Troubleshooting

If the chat widget doesn't appear:
1. Verify the script URL is correct
2. Check browser console for any errors
3. Ensure the script is loaded after your page content
4. Verify your internet connection

## Support

For integration support, contact: <EMAIL>
