# Redis Fallback Behavior

This document explains how the Rylie AI platform handles Redis connectivity and the in-memory fallback mechanism.

## Overview

Rylie AI uses Redis for caching, session storage, and message queuing. However, the system is designed to gracefully degrade to in-memory alternatives when <PERSON><PERSON> is unavailable or not configured.

## Configuration Options

Redis connectivity is controlled through the following environment variables:

```
# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_TLS_ENABLED=false
SKIP_REDIS=true  # Set to true to use in-memory fallback instead of Redis
```

## Fallback Mechanism

When Redis is unavailable or `SKIP_REDIS=true` is set, the system automatically switches to these alternatives:

1. **Session Storage**: Uses in-memory session store (not suitable for multi-server deployments)
2. **Caching**: Uses local memory cache with LRU eviction policy
3. **Message Queue**: Uses in-memory queue implementation

## When to Use Redis vs. Fallback

### Use Redis (SKIP_REDIS=false) when:
- Deploying to production environments
- Running multiple server instances
- Requiring persistent session data across restarts
- Needing distributed caching capabilities
- Processing high volumes of background tasks

### Use Fallback (SKIP_REDIS=true) when:
- Developing locally without Redis installed
- Running in simple single-server deployments
- Testing functionality without external dependencies
- Running in environments where Redis cannot be installed

## Startup Behavior

On application startup, the system:

1. Checks for the `REDIS_HOST` environment variable
2. If not found, automatically sets `SKIP_REDIS=true`
3. Logs a message indicating the use of in-memory fallback
4. Initializes appropriate service implementations based on configuration

## Performance Implications

The in-memory fallback provides adequate functionality for development and testing but has limitations:

- No persistence across server restarts
- Limited storage capacity (constrained by server memory)
- No distribution across multiple server instances
- Potential memory leaks if not properly managed

For production deployments, a proper Redis setup is strongly recommended.

## Troubleshooting

If experiencing issues with Redis connectivity:

1. Verify Redis server is running: `redis-cli ping`
2. Check connection parameters in environment variables
3. Ensure network connectivity between application and Redis server
4. Temporarily enable fallback with `SKIP_REDIS=true` to isolate issues